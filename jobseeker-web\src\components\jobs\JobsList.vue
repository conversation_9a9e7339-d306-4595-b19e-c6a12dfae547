<script setup>
import { ref, onMounted, watch, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useJobsStore } from '@/stores/jobs'
import { useApplicationsStore } from '@/stores/applications'
import Select from 'primevue/select'
import Button from 'primevue/button'
import SpeedDial from 'primevue/speeddial';
import JobsFilterDrawer from './JobsFilterDrawer.vue'
import Avatar from 'primevue/avatar'
import Tag from 'primevue/tag'
import JobsFilter from './JobsFilter.vue'
import alertManager from '@/utils/alertManager'
import { useAppStore } from '@/stores/app'

const router = useRouter()
const route = useRoute()
const { t } = useI18n()
const jobsStore = useJobsStore()
const applicationsStore = useApplicationsStore()
const appStore = useAppStore();
const isMobile = ref(!!appStore.isMobile)


watch(() => appStore.isMobile, (newValue) => {
  isMobile.value = newValue;
})

// Mobile filter drawer state
const showMobileFilters = ref(false)

// Hide sidebar filter when drawer is open
const showSidebarFilter = computed(() => !showMobileFilters.value && !isMobile.value)

// Filters state
const filters = ref({
  search: '',
  location: '',
  industry: null,
  jobType: [],
  experienceLevel: null,
  paymentType: null,
  status: [],
  isUrgent: null,
  isRemote: null,
  minVacancies: null
})

// Sorting
const sortBy = ref('postedDate')
const sortOptions = [
  { label: t('jobs.mostRecent'), value: 'postedDate' },
  { label: t('jobs.salaryHighToLow'), value: 'salary' },
  { label: t('jobs.companyName'), value: 'company' },
  { label: 'Most Vacancies', value: 'vacancies' }
]

const handleFiltersUpdate = (updatedFilters) => {
  filters.value = updatedFilters
  jobsStore.setFilters(updatedFilters)
  jobsStore.setPage(1)
  jobsStore.fetchJobs()
}

const handleClearFilters = () => {
  filters.value = {
    search: '',
    location: '',
    industry: null,
    jobType: [],
    experienceLevel: null,
    paymentType: null,
    status: [],
    isUrgent: null,
    isRemote: null,
    minVacancies: null
  }
  jobsStore.clearFilters()
  jobsStore.fetchJobs()
}

const updateSort = () => {
  jobsStore.setFilters({
    ...filters.value,
    sortBy: sortBy.value,
    sortOrder: sortBy.value === 'salary' ? 'desc' : 'desc'
  })
  jobsStore.setPage(1)
  jobsStore.fetchJobs()
}

const loadMoreJobs = () => {
  if (jobsStore.pagination.hasNext) {
    jobsStore.setPage(jobsStore.pagination.page + 1)
    jobsStore.fetchJobs()
  }
}

const formatDate = (dateString) => {
  if (!dateString) return 'just now';
 
  const date = new Date(dateString);
  const now = new Date();
  const diffTime = Math.abs(now.getTime() - date.getTime());
 
  const diffSeconds = Math.ceil(diffTime / 1000);
  const diffMinutes = Math.ceil(diffTime / (1000 * 60));
  const diffHours = Math.ceil(diffTime / (1000 * 60 * 60));
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
 
  if (diffSeconds < 60) return 'just now';
  if (diffMinutes < 60) return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} ago`;
  if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
  if (diffDays === 1) return '1 day ago';
  if (diffDays < 7) return `${diffDays} days ago`;
  if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`;
  if (diffDays < 365) return `${Math.floor(diffDays / 30)} months ago`;
  return `${Math.floor(diffDays / 365)} years ago`;
}

const hasAdditionalInfo = (job) => {
  return job.workingHours || job.accommodation || job.transportation || job.foodProvided
}

const getStatusSeverity = (status) => {
  switch (status) {
    case 'ACTIVE': return 'success'
    case 'DRAFT': return 'warning'
    case 'CLOSED': return 'danger'
    case 'PAUSED': return 'secondary'
    default: return 'info'
  }
}

const isJobSaved = (jobId) => jobsStore.isJobSaved(jobId)

const hasApplied = (jobId) => {
  const app = applicationsStore.applications.find(app => app && app.jobId === jobId)
  return app && (app.status === 'APPLIED' || app.status === 'PENDING')
}

const viewJobDetails = (job) => {
  router.push(`/jobseeker/jobs/${job.id}`)
}

const saveJob = (job) => {
  if (jobsStore.isJobSaved(job.id)) {
    jobsStore.unsaveJob(job.id)
  } else {
    jobsStore.saveJob(job)
  }
}

const applyToJob = (job) => {
  router.push(`/jobseeker/jobs/${job.id}`)
}

const showFiltersClick = () => {
  showMobileFilters.value = true
}

// Handle URL parameters for category filtering
onMounted(() => {
  if (route.query.category) {
    filters.value.industry = route.query.category
    handleFiltersUpdate(filters.value)
  } else {
    jobsStore.fetchJobs()
  }
})

const highlightedJobIds = computed(() => {
  const search = (filters.value.search || '').trim().toLowerCase();
  const location = (filters.value.location || '').trim().toLowerCase();
  const industry = filters.value.industry ? filters.value.industry.toLowerCase() : '';
  const jobTypes = Array.isArray(filters.value.jobType) ? filters.value.jobType.map(jt => jt.toLowerCase()) : [];
  const experience = filters.value.experienceLevel ? filters.value.experienceLevel.toLowerCase() : '';
  const paymentType = filters.value.paymentType ? filters.value.paymentType.toLowerCase() : '';
  const salaryMin = filters.value.salaryMin || 0;
  const salaryMax = filters.value.salaryMax || 10000000;

  // If all filters are empty/default, do not highlight anything
  const noFilters =
    !search &&
    !location &&
    !industry &&
    !jobTypes.length &&
    !experience &&
    !paymentType &&
    (!filters.value.salaryMin || filters.value.salaryMin === 0) &&
    (!filters.value.salaryMax || filters.value.salaryMax === 10000000);
  if (noFilters) return [];

  return jobsStore.jobs.filter(job => {
    // Job Title, Company, Skills (search)
    let matchesSearch = false;
    if (search) {
      matchesSearch = (job.title && job.title.toLowerCase().includes(search)) ||
        (job.company && job.company.toLowerCase().includes(search)) ||
        (Array.isArray(job.skills) && job.skills.some(skill => skill.toLowerCase().includes(search)));
    } else {
      matchesSearch = true;
    }
    // Location
    let matchesLocation = !location || (job.location && job.location.toLowerCase().includes(location));
    // Industry
    let matchesIndustry = !industry || (job.industry && (job.industry.name || '').toLowerCase().includes(industry));
    // Job Type
    let matchesJobType = !jobTypes.length || (jobTypes.includes((job.jobType || '').toLowerCase()));
    // Experience Level
    let matchesExperience = !experience || ((job.experienceLevel || '').toLowerCase() === experience);
    // Payment Type
    let matchesPayment = !paymentType || ((job.paymentType || '').toLowerCase() === paymentType);
    // Salary
    let jobSalary = parseInt(job.salary, 10);
    if (isNaN(jobSalary)) jobSalary = 0;
    let matchesSalary = jobSalary >= salaryMin && jobSalary <= salaryMax;

    return matchesSearch && matchesLocation && matchesIndustry && matchesJobType && matchesExperience && matchesPayment && matchesSalary;
  }).map(job => job.id);
});
</script>
<template>
  <div class="jobs-page flex flex-1 gap-4 overflow-x-hidden overflow-y-auto">
    <SpeedDial
      direction="up"
      :style="{ position: 'fixed', right: '3.5rem', bottom: '2.5rem', zIndex: 1000 }"
      :tooltipOptions="{ position: 'left' }"
      @click="showFiltersClick"
    >
      <template #button="{ toggleCallback }">
        <Button icon="pi pi-filter" @click="toggleCallback" rounded raised />
      </template>
    </SpeedDial>
    <div class="flex-1">

      <div class="content-panel">
        <div class="content-header">

          <div class="results-info">
            <span class="results-count">{{ t('jobs.jobsFound', { count: jobsStore.totalJobs }) }}</span>
            <div class="sort-controls">
              <Select v-model="sortBy" @change="updateSort" :options="sortOptions" optionLabel="label"
                optionValue="value" :placeholder="t('jobs.sortBy')" class="sort-dropdown" />
            </div>
          </div>
        </div>

        <div class="jobs-list" v-if="jobsStore.hasJobs">
          <div v-for="(job, index) in jobsStore.jobs" :key="job.id"
            :class="['job-card', { 'featured': job.isFeatured, 'urgent': job.isUrgent, 'highlighted-job': highlightedJobIds.includes(job.id) }]">
            <div class="job-header">
              <div class="job-avatar">
                <Avatar :label="job.company?.charAt(0).toUpperCase()" :style="{ backgroundColor: job.color }"
                  shape="circle" size="large" />
              </div>
              <div class="job-basic-info">
                <h3 class="job-title" @click="viewJobDetails(job)">{{ job.title }}</h3>
                <p class="job-company">{{ job.company }}</p>
                <div class="job-badges">
                  <Tag v-if="job.isFeatured" :value="t('jobs.featured')" severity="success" class="featured-badge" />
                  <Tag v-if="job.urgency === 'URGENT'" :value="t('jobs.urgent')" severity="danger" />
                  <Tag :value="job.paymentType" severity="info" />
                </div>
              </div>
              <div class="job-actions">
                <Button icon="pi pi-heart" text size="small" @click="saveJob(job)"
                  v-tooltip.top="t('dashboard.saveJob')" :class="{ 'saved': isJobSaved(job.id) }" />
                <Button icon="pi pi-external-link" text size="small" @click="viewJobDetails(job)"
                  v-tooltip.top="t('common.view') + ' Details'" />
              </div>
            </div>

            <div class="job-content">
              <div class="job-description" v-html="job.description"></div>

              <div class="job-details">
                <div class="job-meta">
                  <span class="job-location">
                    <i class="pi pi-map-marker"></i>
                    {{ job.location }}
                  </span>
                  <span class="job-salary">
                    <i class="pi pi-indian-rupee"></i>
                    ₹{{ job.salary.toLocaleString() }} {{ job.paymentType.toLowerCase() }}
                  </span>
                  <span class="job-posted">
                    <i class="pi pi-calendar"></i>
                    {{ t('jobs.postedAgo', { time: formatDate(job.postedDate || job.createdAt) }) }}
                  </span>
                  <span class="job-vacancies">
                    <i class="pi pi-users"></i>
                    {{ job.vacancies }} {{ job.vacancies === 1 ? 'position' : 'positions' }}
                  </span>
                </div>

                <!-- Additional Job Info -->
                <div class="job-additional-info" v-if="hasAdditionalInfo(job)">
                  <div class="info-items">
                    <span v-if="job.workingHours" class="info-item">
                      <i class="pi pi-clock"></i>
                      {{ job.workingHours }}
                    </span>
                    <span v-if="job.accommodation" class="info-item">
                      <i class="pi pi-home"></i>
                      Accommodation
                    </span>
                    <span v-if="job.transportation" class="info-item">
                      <i class="pi pi-car"></i>
                      Transportation
                    </span>
                    <span v-if="job.foodProvided" class="info-item">
                      <i class="pi pi-shopping-cart"></i>
                      Food Provided
                    </span>
                  </div>
                </div>

                <div class="job-skills" v-if="job.skills && job.skills.length > 0">
                  <Tag v-for="skill in job.skills.slice(0, 4)" :key="skill" :value="skill" class="skill-tag" />
                  <span v-if="job.skills.length > 4" class="more-skills">
                    +{{ job.skills.length - 4 }} more
                  </span>
                </div>
              </div>
            </div>

            <div class="job-footer">
              <div class="job-stats">
                <span class="applicants-count">
                  <i class="pi pi-users"></i>
                  {{ t('jobs.applicants', { count: job.applicationsCount || 0 }) }}
                </span>
                <span class="views-count">
                  <i class="pi pi-eye"></i>
                  {{ t('jobs.views', { count: job.views || 0 }) }}
                </span>
                <span class="job-status">
                  <Tag :value="job.status" :severity="getStatusSeverity(job.status)" class="status-tag" />
                </span>
              </div>
              <Button @click="applyToJob(job)" :label="hasApplied(job.id) ? t('jobs.applied') : t('dashboard.apply')"
                class="apply-button" :disabled="hasApplied(job.id)">
                <template v-if="hasApplied(job.id)">
                  <i class="pi pi-check"></i>
                  {{ t('jobs.applied') }}
                </template>
              </Button>
            </div>
          </div>
        </div>

        <!-- Load More Button -->
        <div v-if="jobsStore.hasJobs && jobsStore.pagination.hasNext" class="load-more-section">
          <Button @click="loadMoreJobs" outlined size="large" :loading="jobsStore.isLoading" class="load-more-btn"
            :label="t('jobs.loadMore')" icon="pi pi-plus" />
        </div>

        <div v-else-if="!jobsStore.isLoading" class="empty-state">
          <i class="pi pi-briefcase empty-icon"></i>
          <h3>{{ t('jobs.noJobsFound') }}</h3>
          <p>{{ t('jobs.adjustFilters') }}</p>
          <Button @click="handleClearFilters" outlined :label="t('jobs.clearFilters')" icon="pi pi-filter-slash" />
        </div>

        <div v-if="jobsStore.isLoading" class="loading-state">
          <i class="pi pi-spin pi-spinner loading-icon"></i>
          <p>{{ t('jobs.findingOpportunities') }}</p>
        </div>
      </div>
    </div>
    <JobsFilterDrawer
      :visible="showMobileFilters"
      :filters="filters"
      @update:visible="showMobileFilters = $event"
      @update:filters="handleFiltersUpdate"
      @clear-filters="handleClearFilters"
    />
  </div>
</template>

<style scoped>
.filter-container {
  position: relative;
}

.toggle-filter-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  cursor: pointer;
  font-weight: bold;
  position: absolute !important;
  z-index: 1;
  top: 10rem;
  margin-left: -1rem;
}

.jobs-page {
  background: linear-gradient(135deg, var(--jobs-bg-start) 0%, var(--jobs-bg-end) 100%);
  transition: background var(--transition-duration) ease;
  padding: 1rem;
}

.jobs-content-expanded {
  display: grid;
  gap: 2rem;
  grid-template-columns: 320px 1fr;
}

.jobs-content-collapsed {
  display: block;
}

.content-panel {
  background: var(--surface-card);
  border: 1px solid var(--surface-border);
  border-radius: 12px;
  padding: 1.5rem;
}

.content-header {
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--surface-border);
}

.page-title {
  margin-bottom: 1rem;
}

.page-title h1 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-color);
  margin: 0 0 0.25rem 0;
}

.page-title p {
  color: var(--text-color-secondary);
  margin: 0;
  font-size: 0.95rem;
}

.mobile-filter-btn {
  display: none;
  margin-bottom: 1rem;
}

.results-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.results-count {
  font-weight: 600;
  color: var(--text-color);
}

.sort-dropdown {
  min-width: 180px;
}

.jobs-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.job-card {
  padding: 1.5rem;
  border: 1px solid var(--surface-border);
  border-radius: 12px;
  transition: all var(--transition-duration) ease;
  background: var(--surface-0);
}

.job-card:hover {
  border-color: var(--primary-color);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.job-card.featured {
  border-color: var(--green-500);
  background: linear-gradient(135deg, var(--surface-0) 0%, rgba(34, 197, 94, 0.02) 100%);
}

.job-card.urgent {
  border-color: var(--red-500);
  background: linear-gradient(135deg, var(--surface-0) 0%, rgba(239, 68, 68, 0.02) 100%);
}

.job-card.highlighted-job {
  border: 2px solid var(--primary-color, #2196f3) !important;
  box-shadow: 0 0 8px 2px rgba(33, 150, 243, 0.15);
  background: #e3f2fd;
}

.job-header {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1rem;
}

.job-basic-info {
  flex: 1;
  min-width: 0;
}

.job-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 0.25rem;
  cursor: pointer;
}

.job-title:hover {
  color: var(--primary-color);
}

.job-company {
  color: var(--primary-color);
  font-weight: 500;
  margin-bottom: 0.75rem;
  font-size: 1rem;
}

.job-badges {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.featured-badge,
.urgent-badge,
.job-type-badge,
.payment-type-badge {
  font-size: 0.75rem !important;
}

.job-actions {
  display: flex;
  gap: 0.25rem;
}

.job-actions .saved {
  color: var(--red-500) !important;
}

.job-content {
  margin-bottom: 1rem;
}

.job-description {
  color: var(--text-color-secondary);
  font-size: 0.9rem;
  margin-bottom: 1rem;
  line-height: 1.5;
}

.job-details {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.job-meta {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.job-location,
.job-salary,
.job-posted,
.job-vacancies {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: var(--text-color-secondary);
  font-size: 0.85rem;
}

.job-location i,
.job-posted i,
.job-vacancies i {
  color: var(--primary-color);
  font-size: 0.75rem;
}

.job-salary {
  font-weight: 600;
  color: var(--primary-color);
}

.job-salary i {
  color: var(--primary-color);
  font-size: 0.75rem;
}

.job-additional-info {
  margin: 0.75rem 0;
}

.info-items {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.8rem;
  color: var(--text-color-secondary);
  background: var(--surface-100);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.info-item i {
  color: var(--primary-color);
  font-size: 0.7rem;
}

.job-skills {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.skill-tag {
  font-size: 0.75rem !important;
  background: var(--surface-100) !important;
  color: var(--text-color-secondary) !important;
}

.more-skills {
  font-size: 0.8rem;
  color: var(--text-color-secondary);
  font-style: italic;
}

.job-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 1rem;
  border-top: 1px solid var(--surface-border);
}

.job-stats {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.applicants-count,
.views-count {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: var(--text-color-secondary);
  font-size: 0.8rem;
}

.applicants-count i,
.views-count i {
  color: var(--primary-color);
  font-size: 0.75rem;
}

.job-status {
  margin-left: auto;
}

.status-tag {
  font-size: 0.75rem !important;
}

.apply-button {
  background: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
  font-weight: 600 !important;
}

.apply-button:disabled {
  background: var(--green-500) !important;
  border-color: var(--green-500) !important;
  opacity: 1 !important;
}

.load-more-section {
  display: flex;
  justify-content: center;
  padding: 2rem 0;
}

.load-more-btn {
  min-width: 200px;
}

.empty-state,
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
}

.empty-icon,
.loading-icon {
  font-size: 3rem;
  color: var(--text-color-secondary);
  margin-bottom: 1rem;
}

.empty-state h3 {
  color: var(--text-color);
  margin-bottom: 0.5rem;
}

.empty-state p {
  color: var(--text-color-secondary);
  margin-bottom: 1.5rem;
}

/* Mobile Filter Drawer Styles */
.mobile-filter-drawer {
  width: 90vw !important;
  max-width: 400px !important;
}

.mobile-filter-content {
  background: transparent !important;
  border: none !important;
  padding: 0 !important;
  position: static !important;
  height: auto !important;
  max-height: none !important;
  overflow: visible !important;
}

.drawer-footer {
  display: flex;
  gap: 1rem;
  padding: 1rem 0 0 0;
  border-top: 1px solid var(--surface-border);
  margin-top: 1rem;
}

.clear-btn {
  flex: 1;
}

.apply-filters-btn {
  flex: 2;
  background: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

@media (max-width: 1200px) {
  .jobs-content-expanded {
    grid-template-columns: 280px 1fr;
    gap: 1.5rem;
  }
}

@media (max-width: 768px) {
  .mobile-filter-btn {
    display: flex !important;
  }

  .jobs-content-expanded {
    grid-template-columns: 1fr;
    gap: 1rem;
    padding: 1rem;
  }

  .job-actions {
    align-self: flex-end;
  }

  .job-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .job-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .apply-button {
    width: 100%;
  }

  .sort-dropdown {
    min-width: 140px;
  }
}

@media (max-width: 480px) {
  .jobs-content-expanded {
    padding: 0;
  }

  .toggle-filter-btn {
    display: none !important;
  }

  .mobile-filter-drawer {
    width: 95vw !important;
  }

  .drawer-footer {
    flex-direction: column;
  }

  .clear-btn,
  .apply-filters-btn {
    flex: none;
  }
}
</style>