<template>
  <Dialog 
    :visible="localVisible" 
    @update:visible="localVisible = $event"
    :header="isEditing ? 'Edit Work Experience' : 'Add Work Experience'"
    :modal="true"
    :closable="true"
    class="experience-dialog"
    :style="{ width: '600px' }"
    @hide="handleCancel"
  >
    <form @submit.prevent="handleSubmit" class="experience-form">
      <div class="form-grid">
        <div class="form-group full-width">
          <label for="jobTitle">Job Title *</label>
          <InputText
            id="jobTitle"
            v-model="formData.jobTitle"
            :class="{ 'p-invalid': errors.jobTitle }"
            placeholder="e.g., Construction Worker"
          />
          <small v-if="errors.jobTitle" class="p-error">{{ errors.jobTitle }}</small>
        </div>

        <div class="form-group full-width">
          <label for="company">Company Name *</label>
          <InputText
            id="company"
            v-model="formData.company"
            :class="{ 'p-invalid': errors.company }"
            placeholder="e.g., ABC Construction Co."
          />
          <small v-if="errors.company" class="p-error">{{ errors.company }}</small>
        </div>

        <div class="form-group">
          <label for="startDate">Start Date *</label>
          <Calendar
            id="startDate"
            v-model="formData.startDate"
            :class="{ 'p-invalid': errors.startDate }"
            placeholder="Select start date"
            :maxDate="maxDate"
            view="month"
            dateFormat="mm/yy"
            showIcon
          />
          <small v-if="errors.startDate" class="p-error">{{ errors.startDate }}</small>
        </div>

        <div class="form-group">
          <label for="endDate">End Date</label>
          <div class="end-date-wrapper">
            <Calendar
              id="endDate"
              v-model="formData.endDate"
              :class="{ 'p-invalid': errors.endDate }"
              placeholder="Select end date"
              :maxDate="maxDate"
              :disabled="formData.currentJob"
              view="month"
              dateFormat="mm/yy"
              showIcon
            />
            <div class="current-job-checkbox">
              <Checkbox
                id="currentJob"
                v-model="formData.currentJob"
                :binary="true"
              />
              <label for="currentJob">Current Job</label>
            </div>
          </div>
          <small v-if="errors.endDate" class="p-error">{{ errors.endDate }}</small>
        </div>

        <div class="form-group full-width">
          <label for="description">Job Description</label>
          <Textarea
            id="description"
            v-model="formData.description"
            rows="4"
            placeholder="Describe your responsibilities and achievements..."
            :maxlength="500"
          />
          <small class="char-count">{{ (formData.description || '').length }}/500 characters</small>
        </div>
      </div>
    </form>

    <template #footer>
      <div class="dialog-footer">
        <Button 
          @click="handleCancel"
          label="Cancel"
          outlined
          :disabled="isSaving"
        />
        <Button 
          @click="handleSubmit"
          :label="isEditing ? 'Update Experience' : 'Add Experience'"
          :loading="isSaving"
          class="save-btn"
        />
      </div>
    </template>
  </Dialog>
</template>

<script setup>
import { ref, watch, computed } from 'vue'
import Dialog from 'primevue/dialog'
import InputText from 'primevue/inputtext'
import Calendar from 'primevue/calendar'
import Textarea from 'primevue/textarea'
import Checkbox from 'primevue/checkbox'
import Button from 'primevue/button'
import alertManager from '@/utils/alertManager'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  experienceData: {
    type: Object,
    default: () => ({})
  },
  isEditing: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:visible', 'save'])

const isSaving = ref(false)
const errors = ref({})

const formData = ref({
  id: null,
  jobTitle: '',
  company: '',
  startDate: null,
  endDate: null,
  currentJob: false,
  description: ''
})

// Computed property to handle v-model for visible prop
const localVisible = computed({
  get() {
    return props.visible
  },
  set(value) {
    emit('update:visible', value)
  }
})

const maxDate = computed(() => new Date())

// Parse YYYY-MM format to Date object
function parseMonthYear(dateStr) {
  if (!dateStr) return null
  const [year, month] = dateStr.split('-').map(Number)
  if (isNaN(year) || isNaN(month)) return null
  return new Date(year, month - 1)
}

// Format Date to YYYY-MM
function formatMonthYear(date) {
  if (!date) return null
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  return `${year}-${month.toString().padStart(2, '0')}`
}

const resetForm = () => {
  formData.value = {
    id: null,
    jobTitle: '',
    company: '',
    startDate: null,
    endDate: null,
    currentJob: false,
    description: ''
  }
  errors.value = {}
}

// Watch for prop changes
watch(() => props.experienceData, (newData) => {
  if (newData && props.isEditing) {
    formData.value = {
      id: newData.id,
      jobTitle: newData.jobTitle || '',
      company: newData.company || '',
      startDate: newData.startDate ? parseMonthYear(newData.startDate) : null,
      endDate: newData.endDate ? parseMonthYear(newData.endDate) : null,
      currentJob: !newData.endDate,
      description: newData.description || ''
    }
  } else {
    resetForm()
  }
}, { immediate: true, deep: true })

const validateForm = () => {
  errors.value = {}
  
  if (!formData.value.jobTitle?.trim()) {
    errors.value.jobTitle = 'Job title is required'
  }
  
  if (!formData.value.company?.trim()) {
    errors.value.company = 'Company name is required'
  }
  
  if (!formData.value.startDate) {
    errors.value.startDate = 'Start date is required'
  } else {
    // MM/YYYY format check
    const start = formData.value.startDate
    const mmYYYY = /^(0[1-9]|1[0-2])\/[0-9]{4}$/
    const formatted = `${(start.getMonth()+1).toString().padStart(2,'0')}/${start.getFullYear()}`
    if (!mmYYYY.test(formatted)) {
      errors.value.startDate = 'Start date must be in MM/YYYY format'
    }
  }
  
  if (!formData.value.currentJob && !formData.value.endDate) {
    errors.value.endDate = 'Please provide an end date or mark as current job'
  } else if (formData.value.endDate) {
    // MM/YYYY format check
    const end = formData.value.endDate
    const mmYYYY = /^(0[1-9]|1[0-2])\/[0-9]{4}$/
    const formatted = `${(end.getMonth()+1).toString().padStart(2,'0')}/${end.getFullYear()}`
    if (!mmYYYY.test(formatted)) {
      errors.value.endDate = 'End date must be in MM/YYYY format'
    }
  }
  
  if (formData.value.startDate && formData.value.endDate && 
      formData.value.startDate > formData.value.endDate) {
    errors.value.endDate = 'End date cannot be before start date'
  }
  
  return Object.keys(errors.value).length === 0
}

const handleSubmit = async () => {
  if (!validateForm()) {
    return
  }
  
  isSaving.value = true
  
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const updatedData = {
      ...formData.value,
      startDate: formData.value.startDate ? `${(formData.value.startDate.getMonth()+1).toString().padStart(2,'0')}/${formData.value.startDate.getFullYear()}` : null,
      endDate: formData.value.currentJob ? null : (formData.value.endDate ? `${(formData.value.endDate.getMonth()+1).toString().padStart(2,'0')}/${formData.value.endDate.getFullYear()}` : null)
    }
    
    emit('save', updatedData)
    emit('update:visible', false)
    
    const message = props.isEditing ? 
      'Work experience updated successfully!' : 
      'Work experience added successfully!'
    
    alertManager.showSuccess('Success', message)
  } catch (error) {
    alertManager.showError('Error', 'Failed to save work experience. Please try again.')
  } finally {
    isSaving.value = false
  }
}

const handleCancel = () => {
  emit('update:visible', false)
  resetForm()
}
</script>

<style scoped>
.experience-dialog {
  border-radius: 12px !important;
}

.experience-form {
  padding: 1rem 0;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  font-weight: 600;
  color: var(--text-color);
  font-size: 0.9rem;
}

.form-group input,
.form-group .p-calendar,
.form-group .p-inputtextarea {
  width: 100% !important;
}

.end-date-wrapper {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.current-job-checkbox {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.current-job-checkbox label {
  font-weight: normal;
  font-size: 0.85rem;
}

.char-count {
  font-size: 0.8rem;
  color: var(--text-color-secondary);
  text-align: right;
}

.dialog-footer {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.save-btn {
  background: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
  }
  
  .dialog-footer {
    flex-direction: column;
  }
}
</style>