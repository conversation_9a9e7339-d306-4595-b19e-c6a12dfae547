import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ForbiddenException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { JobEntity, JobStatus } from '../entities/job.entity';
import { JobApplicationEntity, ApplicationStatus } from '../entities/job-application.entity';
import { JobFavoriteEntity } from '../entities/job-favorite.entity';
import { UserEntity } from '../../users/entities/user.entity';
import { CreateJobApplicationDto } from '../dto/create-job-application.dto';
import { PaginationDto } from '../../common/dto/pagination.dto';
import { PaginatedResponseDto } from '../../common/dto/paginated-response.dto';
import { UserRole } from '../../users/enums/user.enum';
import { Not } from 'typeorm';
import { NotificationsService } from '../../notifications/notifications.service';

@Injectable()
export class JobApplicationService {
  constructor(
    @InjectRepository(JobEntity)
    private readonly jobRepository: Repository<JobEntity>,
    @InjectRepository(JobApplicationEntity)
    private readonly applicationRepository: Repository<JobApplicationEntity>,
    @InjectRepository(JobFavoriteEntity)
    private readonly favoriteRepository: Repository<JobFavoriteEntity>,
    @InjectRepository(UserEntity)
    private readonly userRepository: Repository<UserEntity>,
    private readonly notificationsService: NotificationsService,
  ) {}

  async applyForJob(
    userId: string,
    jobId: string,
    createApplicationDto: CreateJobApplicationDto,
  ): Promise<JobApplicationEntity> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const job = await this.jobRepository.findOne({
      where: { id: jobId, isDeleted: false },
    });

    if (!job) {
      throw new NotFoundException('Job not found');
    }

    if (job.status !== JobStatus.ACTIVE) {
      throw new BadRequestException('Cannot apply for inactive jobs');
    }

    // Check if user has already applied (block only if not WITHDRAWN)
    const existingApplication = await this.applicationRepository.findOne({
      where: {
        job: { id: jobId },
        applicant: { id: userId },
        status: Not(ApplicationStatus.WITHDRAWN),
      },
    });

    if (existingApplication) {
      throw new BadRequestException('You have already applied for this job');
    }

    // Check for withdrawn application
    const withdrawnApplication = await this.applicationRepository.findOne({
      where: {
        job: { id: jobId },
        applicant: { id: userId },
        status: ApplicationStatus.WITHDRAWN,
      },
    });

    let application;
    if (withdrawnApplication) {
      // If there's a withdrawn application, update its status and other fields
      withdrawnApplication.status = ApplicationStatus.PENDING;
      withdrawnApplication.coverLetter = createApplicationDto.coverLetter;
      withdrawnApplication.resumeUrl = createApplicationDto.resumeUrl;
      withdrawnApplication.adminComment = null;
      application = withdrawnApplication;
    } else {
      // Create new application if no withdrawn application exists
      application = this.applicationRepository.create({
        job,
        applicant: user,
        ...createApplicationDto,
      });
    }

    const savedApplication = await this.applicationRepository.save(application);

    // Notify employer
    await this.notificationsService.notifyJobApplied(
      {
        jobId: job.id,
        applicantId: user.id,
      },
      user,
    );

    return savedApplication;
  }

  async getMyApplications(
    userId: string,
    paginationDto: PaginationDto,
  ): Promise<PaginatedResponseDto<JobApplicationEntity>> {
    const { page = 1, limit = 10 } = paginationDto;
    const skip = (page - 1) * limit;

    const [applications, total] = await this.applicationRepository.findAndCount({
      where: {
        applicant: { id: userId },
      },
      relations: ['job', 'job.industry'],
      order: {
        createdAt: 'DESC',
      },
      skip,
      take: limit,
    });

    const totalPages = Math.ceil(total / limit);

    return {
      items: applications,
      total,
      page,
      limit,
      totalPages,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
    };
  }

  async toggleFavorite(userId: string, jobId: string): Promise<{ isFavorite: boolean }> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const job = await this.jobRepository.findOne({
      where: { id: jobId, isDeleted: false },
    });

    if (!job) {
      throw new NotFoundException('Job not found');
    }

    const existingFavorite = await this.favoriteRepository.findOne({
      where: {
        job: { id: jobId },
        user: { id: userId },
      },
    });

    if (existingFavorite) {
      await this.favoriteRepository.remove(existingFavorite);
      return { isFavorite: false };
    }

    const favorite = this.favoriteRepository.create({
      job,
      user,
    });

    await this.favoriteRepository.save(favorite);
    return { isFavorite: true };
  }

  async getMyFavorites(
    userId: string,
    paginationDto: PaginationDto,
  ): Promise<PaginatedResponseDto<JobEntity>> {
    const { page = 1, limit = 10 } = paginationDto;
    const skip = (page - 1) * limit;

    const [favorites, total] = await this.favoriteRepository.findAndCount({
      where: {
        user: { id: userId },
      },
      relations: ['job', 'job.industry'],
      order: {
        createdAt: 'DESC',
      },
      skip,
      take: limit,
    });

    const totalPages = Math.ceil(total / limit);

    return {
      items: favorites.map((fav) => fav.job),
      total,
      page,
      limit,
      totalPages,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
    };
  }

  async updateApplicationStatus(
    applicationId: string,
    userId: string,
    userRole: UserRole,
    status: ApplicationStatus,
    comment?: string,
  ): Promise<JobApplicationEntity> {
    const application = await this.applicationRepository.findOne({
      where: { id: applicationId },
      relations: ['job', 'job.employer'],
    });

    if (!application) {
      throw new NotFoundException('Application not found');
    }

    // Only employer or admin can update status
    if (
      userRole !== UserRole.ADMIN &&
      userRole !== UserRole.SUPER_ADMIN &&
      application.job.employer.id !== userId
    ) {
      throw new ForbiddenException('Only employer or admin can update application status');
    }

    application.status = status;
    if (comment) {
      application.adminComment = comment;
    }

    return this.applicationRepository.save(application);
  }

  async withdrawApplication(applicationId: string, userId: string): Promise<JobApplicationEntity> {
    const application = await this.applicationRepository.findOne({
      where: { id: applicationId },
      relations: ['applicant'],
    });
    if (!application) {
      throw new NotFoundException('Application not found');
    }
    if (application.applicant.id !== userId) {
      throw new ForbiddenException('Only the applicant can withdraw this application');
    }
    application.status = ApplicationStatus.WITHDRAWN;
    return this.applicationRepository.save(application);
  }

  async getJobApplicants(
    userId: string,
    userRole: UserRole,
    paginationDto: PaginationDto,
    jobId?: string,
  ): Promise<PaginatedResponseDto<JobApplicationEntity>> {
    const { page = 1, limit = 10 } = paginationDto;
    const skip = (page - 1) * limit;

    const whereCondition: any = {};

    // If specific job ID is provided, filter by that job
    if (jobId) {
      whereCondition.job = { id: jobId };
    }

    // For employers, only show applications for their own jobs
    if (userRole === UserRole.EMPLOYER) {
      whereCondition.job = {
        ...whereCondition.job,
        employer: { id: userId },
      };
    }

    // For admins and super admins, show all applications
    // (whereCondition remains empty to show all)

    const [applications, total] = await this.applicationRepository.findAndCount({
      where: whereCondition,
      relations: ['job', 'job.industry', 'applicant', 'applicant.profile'],
      order: {
        createdAt: 'DESC',
      },
      skip,
      take: limit,
    });

    const totalPages = Math.ceil(total / limit);

    return {
      items: applications,
      total,
      page,
      limit,
      totalPages,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
    };
  }
}
