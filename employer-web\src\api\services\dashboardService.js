import httpClient from '../httpClient'

export const dashboardService = {
  // Get dashboard statistics - using mock for now
  async getStats() {
    try {
      const response = await httpClient.get('/jobs/employer/dashboard')
      return response
    } catch (error) {
      throw new Error(error.message || 'Failed to fetch dashboard statistics')
    }
  },

  // Get recent activity - using mock for now
  async getRecentActivity(limit = 10) {
    try {
      const response = await dashboardMock.getRecentActivity(limit)
      return {
        success: response.success,
        data: response.data,
        message: response.message
      }
    } catch (error) {
      throw new Error(error.message || 'Failed to fetch recent activity')
    }
  },

  // Get job recommendations - using mock for now
  async getRecommendations(limit = 5) {
    try {
      const response = await dashboardMock.getRecommendations(limit)
      return {
        success: response.success,
        data: response.data,
        message: response.message
      }
    } catch (error) {
      throw new Error(error.message || 'Failed to fetch recommendations')
    }
  }
}

// TODO: When real API is ready, replace with actual HTTP calls:
/*
import httpClient from '../httpClient'
import { ENDPOINTS } from '../endpoints'

export const dashboardService = {
  async getStats() {
    try {
      const response = await httpClient.get('/jobs/employer/dashboard')
      return {
        success: true,
        data: response.stats || response.data,
        message: response.message
      }
    } catch (error) {
      throw new Error(error.message || 'Failed to fetch dashboard statistics')
    }
  },

  async getRecentActivity(limit = 10) {
    try {
      const response = await httpClient.get(ENDPOINTS.DASHBOARD.RECENT_ACTIVITY, {
        params: { limit }
      })
      return {
        success: true,
        data: response.activities || response.data || [],
        message: response.message
      }
    } catch (error) {
      throw new Error(error.message || 'Failed to fetch recent activity')
    }
  },

  async getRecommendations(limit = 5) {
    try {
      const response = await httpClient.get(ENDPOINTS.DASHBOARD.RECOMMENDATIONS, {
        params: { limit }
      })
      return {
        success: true,
        data: response.recommendations || response.data || [],
        message: response.message
      }
    } catch (error) {
      throw new Error(error.message || 'Failed to fetch recommendations')
    }
  }
}
*/