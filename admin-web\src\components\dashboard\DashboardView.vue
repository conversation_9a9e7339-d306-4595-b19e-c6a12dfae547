<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import Card from 'primevue/card'
import Button from 'primevue/button'
import DataTable from 'primevue/datatable'
import Column from 'primevue/column'
import Tag from 'primevue/tag'
import ProgressBar from 'primevue/progressbar'
import { useAppStore } from '@/stores/app'
import SpeedDial from 'primevue/speeddial';
import { useAuthStore } from '@/stores/auth'
import { dashboardService } from '@/api/services/dashboardService'

const appStore = useAppStore();
const router = useRouter();
const authStore = useAuthStore();
const userRole = computed(() => authStore.userRole);

const loading = ref(false);
const error = ref(null);

// Sample data that would come from the getDashboardData service
const dashboardData = ref({
  statistics: {
    totalJobs: 0,
    jobsByStatus: {}
  },
  recentActivity: {
    jobs: []
  }
})

const adminStats = ref({
  users: 0,
  industries: 0,
  jobs: 0,
  feedback: 0,
  errors: 0,
  welcomePageVisits: 0
});

// Sample job data (would come from the service)
const sampleJobs = [
  {
    id: '1',
    title: 'Senior Software Developer',
    industry: { name: 'Technology' },
    status: 'ACTIVE',
    salary: 75000,
    location: 'New York, NY',
    createdAt: new Date('2024-01-20T10:30:00Z'),
    applicationsCount: 15
  },
  {
    id: '2',
    title: 'Construction Worker',
    industry: { name: 'Construction' },
    status: 'PENDING',
    salary: 25,
    location: 'Los Angeles, CA',
    createdAt: new Date('2024-01-19T14:20:00Z'),
    applicationsCount: 8
  },
  {
    id: '3',
    title: 'Marketing Manager',
    industry: { name: 'Marketing' },
    status: 'CLOSED',
    salary: 65000,
    location: 'Chicago, IL',
    createdAt: new Date('2024-01-18T09:15:00Z'),
    applicationsCount: 23
  },
  {
    id: '4',
    title: 'Data Analyst',
    industry: { name: 'Technology' },
    status: 'ACTIVE',
    salary: 55000,
    location: 'Austin, TX',
    createdAt: new Date('2024-01-17T16:45:00Z'),
    applicationsCount: 12
  },
  {
    id: '5',
    title: 'Nurse Practitioner',
    industry: { name: 'Healthcare' },
    status: 'ACTIVE',
    salary: 80000,
    location: 'Boston, MA',
    createdAt: new Date('2024-01-16T11:30:00Z'),
    applicationsCount: 19
  }
]

// Simulate the service response
const initializeDashboard = () => {
  // Simulate job statistics
  const totalJobs = sampleJobs.length
  const jobsByStatus = sampleJobs.reduce((acc, job) => {
    acc[job.status] = (acc[job.status] || 0) + 1
    return acc
  }, {})

  dashboardData.value = {
    statistics: {
      totalJobs,
      jobsByStatus
    },
    recentActivity: {
      jobs: sampleJobs.slice(0, 5) // Recent 5 jobs
    }
  }
}

const isAdmin = computed(() => userRole.value === 'admin' || userRole.value === 'super_admin');

const jobStats = computed(() => {
  if (isAdmin.value) {
    const stats = dashboardData.value.statistics || {};
    const jobsByStatus = stats.jobsByStatus || {};
    return {
      total: stats.totalJobs || 0,
      active: jobsByStatus.ACTIVE || 0,
      pending: jobsByStatus.PENDING || 0,
      closed: jobsByStatus.CLOSED || 0,
      draft: jobsByStatus.DRAFT || 0
    };
  } else {
    const stats = dashboardData.value.statistics;
    return {
      total: stats.totalJobs,
      active: stats.jobsByStatus.ACTIVE || 0,
      pending: stats.jobsByStatus.PENDING || 0,
      closed: stats.jobsByStatus.CLOSED || 0,
      draft: stats.jobsByStatus.DRAFT || 0
    };
  }
});

const recentJobs = computed(() => {
  if (isAdmin.value) {
    return dashboardData.value.recentActivity?.jobs || [];
  } else {
    return dashboardData.value.recentActivity.jobs;
  }
});

// Recent activities (would come from various services)
const recentActivities = ref([
  {
    title: 'New job posted',
    description: 'Senior Software Developer • Technology',
    icon: 'pi pi-briefcase',
    time: '2 minutes ago',
    type: 'job'
  },
  {
    title: 'User registered',
    description: '<EMAIL> joined as Job Seeker',
    icon: 'pi pi-user-plus',
    time: '15 minutes ago',
    type: 'user'
  },
  {
    title: 'Feedback received',
    description: 'Bug report: Login form validation error',
    icon: 'pi pi-comments',
    time: '1 hour ago',
    type: 'feedback'
  },
  {
    title: 'Industry added',
    description: 'New industry: Renewable Energy',
    icon: 'pi pi-building',
    time: '2 hours ago',
    type: 'industry'
  },
  {
    title: 'System update',
    description: 'Version 2.1.0 deployed successfully',
    icon: 'pi pi-cog',
    time: '3 hours ago',
    type: 'system'
  }
])

// Methods
const getStatusColor = (status) => {
  const colors = {
    ACTIVE: 'success',
    PENDING: 'warning',
    CLOSED: 'danger',
    DRAFT: 'secondary'
  }
  return colors[status] || 'secondary'
}

const formatSalary = (amount) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0
  }).format(amount)
}

const formatDate = (date) => {
  return new Intl.DateTimeFormat('en-US', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date)
}

const getActivityIcon = (type) => {
  const icons = {
    job: 'pi pi-briefcase',
    user: 'pi pi-user-plus',
    feedback: 'pi pi-comments',
    industry: 'pi pi-building',
    system: 'pi pi-cog'
  }
  return icons[type] || 'pi pi-info-circle'
}

const getActivityColor = (type) => {
  const colors = {
    job: 'success',
    user: 'info',
    feedback: 'warning',
    industry: 'secondary',
    system: 'help'
  }
  return colors[type] || 'secondary'
}

const getStatusIcon = (status) => {
  const icons = {
    ACTIVE: 'pi pi-check-circle',
    PENDING: 'pi pi-clock',
    CLOSED: 'pi pi-times-circle',
    DRAFT: 'pi pi-file',
    INACTIVE: 'pi pi-ban',
    // Add more mappings as needed
  };
  return icons[status] || 'pi pi-info-circle';
};

// Initialize dashboard on mount
onMounted(async () => {
  appStore.pageInfo = {
    title: 'Admin Dashboard',
    subTitle: "Welcome back! Here's what's happening with your platform."
  }
  if (userRole.value === 'admin' || userRole.value === 'super_admin') {
    loading.value = true;
    try {
      const res = await dashboardService.getAdminStats();
      if (res.success && res.data) {
        dashboardData.value = {
          statistics: res.data.statistics,
          recentActivity: res.data.recentActivity
        };
        adminStats.value = res.data.adminMetrics;
      } else {
        error.value = res.message || 'Failed to load admin dashboard stats.';
      }
    } catch (err) {
      error.value = err.message || 'Failed to load admin dashboard stats.';
    } finally {
      loading.value = false;
    }
  } else {
    // fallback to existing logic for non-admins
    initializeDashboard();
  }
})

const items = ref([
  {
    label: 'Refresh',
    icon: 'pi pi-refresh',
    severity: 'success',
    command: () => {
      console.log('Refresh')
    }
  },
  {
    label: 'Export',
    icon: 'pi pi-file-export',
    severity: 'warning',
    command: () => {
      console.log('Export')
    }
  },
  {
    label: 'Add User',
    icon: 'pi pi-user-plus',
    severity: 'success',
    command: () => {
      router.push('/jdadmin/users')
    }
  },
  {
    label: 'Create Job',
    icon: 'pi pi-briefcase',
    severity: 'primary',
    command: () => {
      router.push('/jdadmin/jobs')
    }
  },
  {
    label: 'Add Industry',
    icon: 'pi pi-building',
    severity: 'info',
    command: () => {
      router.push('/jdadmin/industries')
    }
  },
  {
    label: 'View Feedback',
    icon: 'pi pi-comments',
    severity: 'help',
    command: () => {
      router.push('/jdadmin/feedback')
    }
  },
  {
    label: 'Error Tracking',
    icon: 'pi pi-exclamation-triangle',
    severity: 'danger',
    command: () => {
      router.push('/jdadmin/errors')
    }
  },
  {
    label: 'System Settings',
    icon: 'pi pi-cog',
    severity: 'help',
    command: () => {
      router.push('/jdadmin/settings')
    }
  }
  
])

// Dynamically compute job status summary for admin users
const jobStatusSummary = computed(() => {
  if (isAdmin.value) {
    const jobsByStatus = dashboardData.value.statistics?.jobsByStatus || {};
    const total = dashboardData.value.statistics?.totalJobs || 0;
    return Object.entries(jobsByStatus).map(([status, count]) => ({
      status,
      count,
      percentage: total > 0 ? Math.round((count / total) * 100) : 0
    }));
  } else {
    // Default to legacy statuses for non-admins
    const stats = jobStats.value;
    return [
      { status: 'ACTIVE', count: stats.active, percentage: stats.total ? Math.round((stats.active / stats.total) * 100) : 0 },
      { status: 'PENDING', count: stats.pending, percentage: stats.total ? Math.round((stats.pending / stats.total) * 100) : 0 },
      { status: 'CLOSED', count: stats.closed, percentage: stats.total ? Math.round((stats.closed / stats.total) * 100) : 0 },
      { status: 'DRAFT', count: stats.draft, percentage: stats.total ? Math.round((stats.draft / stats.total) * 100) : 0 }
    ];
  }
});
</script>

<template>
  <div class="dashboard-view flex-1">

    <!-- Main Statistics Grid -->
    <div class="stats-grid">
      <!-- Job Statistics -->
      <Card class="stat-card">
        <template #content>
          <div class="stat-content">
            <div class="stat-icon jobs">
              <i class="pi pi-briefcase"></i>
            </div>
            <div class="stat-info">
              <h3>{{ jobStats.total }}</h3>
              <p>Total Jobs</p>
              <div class="stat-breakdown">
                <span class="breakdown-item success">{{ jobStats.active }} Active</span>
                <span class="breakdown-item warning">{{ jobStats.pending }} Pending</span>
              </div>
            </div>
          </div>
        </template>
      </Card>

      <!-- User Statistics -->
      <Card class="stat-card">
        <template #content>
          <div class="stat-content">
            <div class="stat-icon users">
              <i class="pi pi-users"></i>
            </div>
            <div class="stat-info">
              <h3>{{ isAdmin ? adminStats.users : adminStats.totalUsers }}</h3>
              <p>Total Users</p>
              <div class="progress-container">
                <span class="progress-text">+12% this month</span>
              </div>
            </div>
          </div>
        </template>
      </Card>

      <!-- Industries -->
      <Card class="stat-card">
        <template #content>
          <div class="stat-content">
            <div class="stat-icon industries">
              <i class="pi pi-building"></i>
            </div>
            <div class="stat-info">
              <h3>{{ isAdmin ? adminStats.industries : adminStats.totalIndustries }}</h3>
              <p>Industries</p>
              <div class="progress-container">
                <span class="progress-text">2 new this week</span>
              </div>
            </div>
          </div>
        </template>
      </Card>

      <!-- System Health -->
      <Card class="stat-card">
        <template #content>
          <div class="stat-content">
            <div class="stat-icon uptime">
              <i class="pi pi-chart-line"></i>
            </div>
            <div class="stat-info">
              <h3>{{ isAdmin ? adminStats.welcomePageVisits : adminStats.systemUptime }}%</h3>
              <p>System Uptime</p>
              <div class="progress-container">
                <ProgressBar :value="isAdmin ? adminStats.welcomePageVisits : adminStats.systemUptime" :show-value="false" class="uptime-progress" />
              </div>
            </div>
          </div>
        </template>
      </Card>
    </div>

    <!-- Dashboard Content Grid -->
    <div class="dashboard-grid">
      <!-- Job Status Summary -->
      <Card class="summary-card">
        <template #title>
          <div class="card-header">
            <h3>Job Status Summary</h3>
            <Button icon="pi pi-refresh" text rounded size="small" @click="initializeDashboard" />
          </div>
        </template>
        <template #content>
          <div class="status-summary">
            <div class="status-item" v-for="item in jobStatusSummary" :key="item.status">
              <div :class="['status-icon', item.status.toLowerCase()]">
                <i :class="getStatusIcon(item.status)"></i>
              </div>
              <div class="status-info">
                <h4>{{ item.count }}</h4>
                <p>{{ item.status.charAt(0) + item.status.slice(1).toLowerCase() }} Jobs</p>
                <span class="status-percentage">{{ item.percentage }}%</span>
              </div>
            </div>
          </div>
        </template>
      </Card>

      <!-- Recent Jobs -->
      <Card class="recent-jobs-card">
        <template #title>
          <div class="card-header">
            <h3>Recent Jobs</h3>
            <Button label="View All" text size="small" @click="$router.push('/jdadmin/jobs')" />
          </div>
        </template>
        <template #content>
          <DataTable :value="recentJobs" class="recent-jobs-table" :paginator="false" :rows="5">
            <Column field="title" header="Job Title">
              <template #body="slotProps">
                <div class="job-title-cell">
                  <span class="job-title">{{ slotProps.data.title }}</span>
                  <span class="job-industry">{{ slotProps.data.industry?.name }}</span>
                </div>
              </template>
            </Column>

            <Column field="status" header="Status">
              <template #body="slotProps">
                <Tag :value="slotProps.data.status" :severity="getStatusColor(slotProps.data.status)" />
              </template>
            </Column>

            <Column field="salary" header="Salary">
              <template #body="slotProps">
                <span class="salary-text">{{ formatSalary(slotProps.data.salary) }}</span>
              </template>
            </Column>

            <Column field="applicationsCount" header="Applications">
              <template #body="slotProps">
                <span class="applications-count">{{ slotProps.data.applicationsCount || 0 }}</span>
              </template>
            </Column>

            <Column field="createdAt" header="Posted">
              <template #body="slotProps">
                <span class="posted-date">{{ formatDate(slotProps.data.createdAt) }}</span>
              </template>
            </Column>
          </DataTable>
        </template>
      </Card>
    </div>

    <!-- Recent Activity & Quick Actions -->
    <div class="bottom-grid">
      <!-- Recent Activity -->
      <Card class="activity-card">
        <template #title>
          <div class="card-header">
            <h3>Recent Activity</h3>
            <Button icon="pi pi-external-link" text rounded size="small" />
          </div>
        </template>
        <template #content>
          <div class="activity-list">
            <div v-for="activity in recentActivities" :key="activity.title" class="activity-item">
              <div :class="['activity-icon', getActivityColor(activity.type)]">
                <i :class="getActivityIcon(activity.type)"></i>
              </div>
              <div class="activity-content">
                <p><strong>{{ activity.title }}</strong></p>
                <span class="activity-description">{{ activity.description }}</span>
                <span class="activity-time">{{ activity.time }}</span>
              </div>
            </div>
          </div>
        </template>
      </Card>
    </div>
    <SpeedDial :model="items" direction="up" :style="{ position: 'absolute', right: '1.5rem', bottom: '1.2rem' }"
      :tooltipOptions="{ position: 'left' }">
      <template #item="{ item }">
        <Button 
        :icon="item.icon" 
        @click="item.command" 
        :severity="item.severity || 'secondary'" 
        class="db-speed-sub-btn"
        v-tooltip.left="item.label"
        raised 
        rounded
        />
      </template>
    </SpeedDial>
  </div>
</template>

<style scoped>
.dashboard-view {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  overflow-y: auto;
  overflow-x: hidden;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.stat-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.stat-icon {
  width: 56px;
  height: 56px;
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  flex-shrink: 0;
}

.stat-icon.jobs {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.stat-icon.users {
  background: linear-gradient(135deg, #f093fb, #f5576c);
}

.stat-icon.industries {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.stat-icon.uptime {
  background: linear-gradient(135deg, #43e97b, #38f9d7);
}

.stat-info {
  flex: 1;
}

.stat-info h3 {
  margin: 0 0 0.25rem 0;
  font-size: 2rem;
  font-weight: 700;
  color: var(--p-text-color);
}

.stat-info p {
  margin: 0 0 0.5rem 0;
  color: var(--p-text-muted-color);
  font-size: 0.875rem;
}

.stat-breakdown {
  display: flex;
  gap: 0.75rem;
}

.breakdown-item {
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.125rem 0.5rem;
  border-radius: 12px;
}

.breakdown-item.success {
  background: var(--p-green-100);
  color: var(--p-green-700);
}

.breakdown-item.warning {
  background: var(--p-yellow-100);
  color: var(--p-yellow-700);
}

.progress-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.progress-text {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--p-text-muted-color);
}

.uptime-progress {
  flex: 1;
  height: 6px;
}

.dashboard-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.bottom-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 1.5rem;
}

.summary-card,
.recent-jobs-card,
.activity-card {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.card-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--p-text-color);
}

.status-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: var(--p-surface-50);
  border-radius: 8px;
  transition: background-color 0.2s ease;
}

.status-item:hover {
  background: var(--p-surface-100);
}

:global(.dark) .status-item {
  background: var(--p-surface-800);
}

:global(.dark) .status-item:hover {
  background: var(--p-surface-700);
}

.status-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  color: white;
  flex-shrink: 0;
}

.status-icon.active {
  background: var(--p-green-500);
}

.status-icon.pending {
  background: var(--p-yellow-500);
}

.status-icon.closed {
  background: var(--p-red-500);
}

.status-icon.draft {
  background: var(--p-surface-500);
}

.status-info h4 {
  margin: 0 0 0.25rem 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--p-text-color);
}

.status-info p {
  margin: 0 0 0.25rem 0;
  color: var(--p-text-muted-color);
  font-size: 0.875rem;
}

.status-percentage {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--p-text-muted-color);
}

.recent-jobs-table {
  font-size: 0.875rem;
}

.job-title-cell {
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
}

.job-title {
  font-weight: 600;
  color: var(--p-text-color);
}

.job-industry {
  font-size: 0.75rem;
  color: var(--p-text-muted-color);
}

.salary-text,
.applications-count,
.posted-date {
  font-size: 0.875rem;
  color: var(--p-text-color);
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  max-height: 400px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: var(--p-surface-50);
  border-radius: 8px;
  transition: background-color 0.2s ease;
}

.activity-item:hover {
  background: var(--p-surface-100);
}

:global(.dark) .activity-item {
  background: var(--p-surface-800);
}

:global(.dark) .activity-item:hover {
  background: var(--p-surface-700);
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  color: white;
  flex-shrink: 0;
}

.activity-icon.success {
  background: var(--p-green-500);
}

.activity-icon.info {
  background: var(--p-blue-500);
}

.activity-icon.warning {
  background: var(--p-yellow-500);
}

.activity-icon.secondary {
  background: var(--p-surface-500);
}

.activity-icon.help {
  background: var(--p-purple-500);
}

.activity-content {
  flex: 1;
}

.activity-content p {
  margin: 0 0 0.25rem 0;
  color: var(--p-text-color);
  font-size: 0.875rem;
}

.activity-description {
  display: block;
  color: var(--p-text-muted-color);
  font-size: 0.75rem;
  margin-bottom: 0.25rem;
}

.activity-time {
  color: var(--p-text-muted-color);
  font-size: 0.75rem;
  font-style: italic;
}

.action-btn {
  justify-content: flex-start;
  text-align: left;
}

@media (max-width: 1200px) {
  .dashboard-grid {
    grid-template-columns: 1fr;
  }

  .bottom-grid {
    grid-template-columns: 1fr;
  }

  .status-summary {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .header-actions {
    justify-content: stretch;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .status-summary {
    grid-template-columns: 1fr;
  }

  .activity-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }
}
</style> 