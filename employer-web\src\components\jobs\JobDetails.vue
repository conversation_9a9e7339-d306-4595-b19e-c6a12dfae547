<template>
    <div class="job-details-page flex flex-1 overflow-x-hidden overflow-y-auto">
      <div v-if="isLoading" class="loading-state">
        <div class="loading-container">
          <i class="pi pi-spin pi-spinner loading-icon"></i>
          <h3>{{ t('common.loading') }}</h3>
          <p>Loading job details...</p>
        </div>
      </div>
      <div v-else-if="error" class="error-state">
        <div class="error-container">
          <i class="pi pi-exclamation-triangle error-icon"></i>
          <h3>{{ t('jobs.jobNotFound') }}</h3>
          <p>{{ error }}</p>
          <div class="error-actions">
            <Button @click="retryFetch" :label="t('auth.tryAgain')" icon="pi pi-refresh" class="retry-button" />
            <Button @click="goBack" :label="t('jobs.backToJobs')" icon="pi pi-arrow-left" outlined />
          </div>
        </div>
      </div>
      <div v-else-if="currentJob" class="job-details-content employer-view  flex-1">
        <div class="job-main">
          <div class="job-header-card">
            <div class="job-header-info">
              <div class="job-badges">
                <Tag v-if="currentJob.isFeatured" :value="t('jobs.featured')" severity="success" />
                <Tag v-if="currentJob.urgency === 'URGENT'" :value="t('jobs.urgent')" severity="danger" />
                <Tag :value="currentJob.status" :severity="getStatusSeverity(currentJob.status)" />
                <Tag :value="currentJob.paymentType" severity="info" />
              </div>
              <h1 class="job-title">{{ currentJob.title }}</h1>
              <div class="job-meta">
                <span class="job-location"><i class="pi pi-map-marker"></i> {{ currentJob.location }}</span>
                <span class="job-type"><i class="pi pi-briefcase"></i> {{ formatJobType(currentJob.jobType) }}</span>
                <span class="job-experience"><i class="pi pi-user"></i> {{ formatExperienceLevel(currentJob.experienceLevel) }}</span>
                <span class="job-posted"><i class="pi pi-calendar"></i> {{ t('jobs.postedAgo', { time: formatDate(currentJob.createdAt) }) }}</span>
                <span class="job-vacancies"><i class="pi pi-users"></i> {{ currentJob.vacancies }} {{ currentJob.vacancies === 1 ? 'position' : 'positions' }}</span>
              </div>
              <div class="job-salary">
                <i class="pi pi-indian-rupee"></i>
                {{ parseFloat(currentJob.salary).toLocaleString() }} / {{ currentJob.paymentType.toLowerCase() }}
              </div>
            </div>
            <div class="job-header-actions">
              <Button v-if="currentJob && currentJob.id" label="Edit" icon="pi pi-pencil" class="edit-button" @click="router.push(`/employer/jobs/${currentJob.id}/edit`)" />
            </div>
          </div>

          <div class="job-section">
            <h3 class="section-title">Description</h3>
            <div class="section-content">
              <div class="job-description" style="white-space: pre-line; word-break: break-word;" v-html="currentJob.description"></div>
            </div>
          </div>

          <div class="job-section" v-if="hasAdditionalInfo()">
            <h3 class="section-title">Additional Information</h3>
            <div class="section-content">
              <div class="additional-info-grid">
                <div v-if="currentJob.workingHours" class="info-item"><i class="pi pi-clock"></i><div><strong>Working Hours</strong><p>{{ currentJob.workingHours }}</p></div></div>
                <div v-if="currentJob.accommodation" class="info-item"><i class="pi pi-home"></i><div><strong>Accommodation</strong><p>{{ formatBooleanValue(currentJob.accommodation) }}</p></div></div>
                <div v-if="currentJob.transportation" class="info-item"><i class="pi pi-car"></i><div><strong>Transportation</strong><p>{{ formatBooleanValue(currentJob.transportation) }}</p></div></div>
                <div v-if="currentJob.foodProvided" class="info-item"><i class="pi pi-shopping-cart"></i><div><strong>Food Provided</strong><p>{{ formatBooleanValue(currentJob.foodProvided) }}</p></div></div>
                <div v-if="currentJob.safetyEquipment" class="info-item"><i class="pi pi-shield"></i><div><strong>Safety Equipment</strong><p>{{ formatBooleanValue(currentJob.safetyEquipment) }}</p></div></div>
                <div v-if="currentJob.trainingProvided" class="info-item"><i class="pi pi-graduation-cap"></i><div><strong>Training Provided</strong><p>{{ formatBooleanValue(currentJob.trainingProvided) }}</p></div></div>
              </div>
            </div>
          </div>

          <div class="job-section" v-if="currentJob.responsibilities && currentJob.responsibilities.length > 0">
            <h3 class="section-title">Key Responsibilities</h3>
            <div class="section-content">
              <div class="responsibilities-grid">
                <Tag v-for="responsibility in currentJob.responsibilities" :key="responsibility" :value="responsibility" class="responsibility-tag" />
              </div>
            </div>
          </div>

          <div class="job-section" v-if="currentJob.requirements && currentJob.requirements.length > 0">
            <h3 class="section-title">Requirements</h3>
            <div class="section-content">
              <div class="requirements-grid">
                <Tag v-for="requirement in currentJob.requirements" :key="requirement" :value="requirement" class="requirement-tag" />
              </div>
            </div>
          </div>

          <div class="job-section" v-if="currentJob.skills && currentJob.skills.length > 0">
            <h3 class="section-title">Required Skills</h3>
            <div class="section-content"><div class="skills-grid"><Tag v-for="skill in currentJob.skills" :key="skill" :value="skill" class="skill-tag" /></div></div>
          </div>

          <div class="job-section" v-if="currentJob.benefits && currentJob.benefits.length > 0">
            <h3 class="section-title">Benefits & Perks</h3>
            <div class="section-content"><div class="benefits-grid"><div v-for="benefit in currentJob.benefits" :key="benefit" class="benefit-item"><i class="pi pi-check benefit-icon"></i><span>{{ benefit }}</span></div></div></div>
          </div>

          <div class="job-section" v-if="currentJob.contactDisplayType || currentJob.contactPerson || currentJob.contactPhone || currentJob.contactEmail">
            <h3 class="section-title">Contact Information</h3>
            <div class="section-content">
              <div v-if="currentJob.contactDisplayType"><strong>Contact Type:</strong> {{ currentJob.contactDisplayType }}</div>
              <div v-if="currentJob.contactPerson"><strong>Contact Person:</strong> {{ currentJob.contactPerson }}</div>
              <div v-if="currentJob.contactPhone"><strong>Contact Phone:</strong> {{ currentJob.contactPhone }}</div>
              <div v-if="currentJob.contactEmail"><strong>Contact Email:</strong> {{ currentJob.contactEmail }}</div>
            </div>
          </div>

          <div class="job-section">
            <h3 class="section-title">Job Meta</h3>
            <div class="section-content">
              <div><strong>Department:</strong> {{ currentJob.industry?.name || 'N/A' }}</div>
              <div v-if="currentJob.subIndustry || currentJob.subIndustryId"><strong>Sub-Industry:</strong> {{ currentJob.subIndustry?.name || getSubIndustryName(currentJob.subIndustryId) || 'N/A' }}</div>
              <div><strong>Job Type:</strong> {{ formatJobType(currentJob.jobType) }}</div>
              <div><strong>Experience Level:</strong> {{ formatExperienceLevel(currentJob.experienceLevel) }}</div>
              <div><strong>Payment Type:</strong> {{ currentJob.paymentType }}</div>
              <div><strong>Posted Date:</strong> {{ formatFullDate(currentJob.createdAt) }}</div>
              <div><strong>Last Updated:</strong> {{ formatFullDate(currentJob.updatedAt) }}</div>
              <div><strong>Status:</strong> {{ currentJob.status }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useJobsStore } from '@/stores/jobs'
import Button from 'primevue/button'
import Tag from 'primevue/tag'
import alertManager from '@/utils/alertManager'
import { formatBooleanValue } from '@/utils/formatters'

const router = useRouter()
const route = useRoute()
const { t } = useI18n()
const jobsStore = useJobsStore()

// Local state
const isLoading = ref(false)
const error = ref(null)
const isJobSaved = ref(false)
const hasApplied = ref(false)

// Get current job from store
const currentJob = computed(() => jobsStore.currentJob)

// Helper functions
const getCompanyName = () => {
  if (!currentJob.value) return ''
  
  const employer = currentJob.value.employer
  if (employer?.firstName && employer?.lastName) {
    return `${employer.firstName} ${employer.lastName}`.trim()
  }
  
  if (employer?.email) {
    return employer.email.split('@')[0].replace(/[._]/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
  }
  
  return 'Unknown Company'
}

const getCompanyInitial = () => {
  const companyName = getCompanyName()
  return companyName.charAt(0).toUpperCase()
}

const getJobColor = () => {
  // Generate a consistent color based on job ID
  const colors = [
    '#3b82f6', '#10b981', '#f59e0b', '#ef4444', 
    '#8b5cf6', '#06b6d4', '#ec4899', '#84cc16'
  ]
  
  if (!currentJob.value?.id) return colors[0]
  
  // Simple hash function to get consistent color
  let hash = 0
  const id = currentJob.value.id.toString()
  for (let i = 0; i < id.length; i++) {
    hash = id.charCodeAt(i) + ((hash << 5) - hash)
  }
  
  return colors[Math.abs(hash) % colors.length]
}

const getApplicantsCount = () => {
  // Mock count since not in API response
  return Math.floor(Math.random() * 50) + 1
}

const getViewsCount = () => {
  // Mock count since not in API response
  return Math.floor(Math.random() * 200) + 10
}

const hasAdditionalInfo = () => {
  if (!currentJob.value) return false
  
  return currentJob.value.workingHours || 
         currentJob.value.accommodation || 
         currentJob.value.transportation || 
         currentJob.value.foodProvided ||
         currentJob.value.safetyEquipment ||
         currentJob.value.trainingProvided
}

const formatJobType = (type) => {
  const typeMap = {
    'FULL_TIME': 'Full-time',
    'PART_TIME': 'Part-time',
    'CONTRACT': 'Contract',
    'TEMPORARY': 'Temporary',
    'FREELANCE': 'Freelance'
  }
  return typeMap[type] || type
}

const formatExperienceLevel = (level) => {
  const levelMap = {
    'FRESHER': 'Entry Level',
    'EXPERIENCED': 'Experienced',
    'EXPERT': 'Expert Level',
    'SENIOR': 'Senior Level'
  }
  return levelMap[level] || level
}

const getStatusSeverity = (status) => {
  switch (status) {
    case 'ACTIVE': return 'success'
    case 'DRAFT': return 'warning'
    case 'CLOSED': return 'danger'
    case 'PAUSED': return 'secondary'
    default: return 'info'
  }
}

const formatDate = (dateString) => {
  if (!dateString) return 'just now';

  const date = new Date(dateString);
  const now = new Date();
  const diffTime = Math.abs(now.getTime() - date.getTime());

  const diffSeconds = Math.ceil(diffTime / 1000);
  const diffMinutes = Math.ceil(diffTime / (1000 * 60));
  const diffHours = Math.ceil(diffTime / (1000 * 60 * 60));
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  if (diffSeconds < 60) return 'just now';
  if (diffMinutes < 60) return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} ago`;
  if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
  if (diffDays === 1) return '1 day ago';
  if (diffDays < 7) return `${diffDays} days ago`;
  if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`;
  if (diffDays < 365) return `${Math.floor(diffDays / 30)} months ago`;
  return `${Math.floor(diffDays / 365)} years ago`;
}

const formatFullDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

// Helper to get sub-industry name by ID if only ID is present
function getSubIndustryName(subIndustryId) {
  if (!subIndustryId || !currentJob.value?.industry?.subIndustries) return '';
  const sub = currentJob.value.industry.subIndustries.find(s => s.id === subIndustryId);
  return sub ? sub.name : '';
}

// Actions
const goBack = () => {
  router.push('/employer/jobs')
}

const saveJob = () => {
  isJobSaved.value = !isJobSaved.value
  console.log('Toggle save job:', currentJob.value?.title)
  
  const message = isJobSaved.value ? 'Job saved successfully!' : 'Job removed from saved list'
  alertManager.showSuccess('Success', message)
}

const applyToJob = () => {
  if (!hasApplied.value) {
    hasApplied.value = true
    console.log('Apply to job:', currentJob.value?.title)
    alertManager.showSuccess('Application Submitted', 'Your application has been submitted successfully!')
  }
}

const shareJob = (method) => {
  const jobUrl = window.location.href
  
  if (method === 'copy') {
    navigator.clipboard.writeText(jobUrl).then(() => {
      alertManager.showSuccess('Link Copied', 'Job URL copied to clipboard')
    }).catch(() => {
      alertManager.showError('Copy Failed', 'Failed to copy link to clipboard')
    })
  } else if (method === 'email') {
    const subject = `Check out this job: ${currentJob.value?.title}`
    const body = `I found this job that might interest you:\n\n${currentJob.value?.title} at ${getCompanyName()}\n\n${jobUrl}`
    window.location.href = `mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`
  }
}

const fetchJobDetails = async (jobId) => {
  isLoading.value = true
  error.value = null
  
  try {
    // Check if job is already in store
    if (currentJob.value && currentJob.value.id === jobId) {
      isLoading.value = false
      return
    }
    
    // Fetch job details from API
    await jobsStore.fetchJobById(jobId)
    
    // Check if this is from an apply action
    if (route.query.action === 'apply') {
      applyToJob()
    }
    
  } catch (err) {
    console.error('Failed to fetch job:', err)
    error.value = err.message || t('jobs.jobNotFoundDesc')
  } finally {
    isLoading.value = false
  }
}

const retryFetch = () => {
  const jobId = route.params.id
  fetchJobDetails(jobId)
}

onMounted(() => {
  const jobId = route.params.id
  fetchJobDetails(jobId)
})
</script>

<style scoped>
.job-details-page {
  background: linear-gradient(135deg, var(--jobs-bg-start) 0%, var(--jobs-bg-end) 100%);
}

.loading-state,
.error-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 1rem;
}

.loading-container,
.error-container {
  text-align: center;
  max-width: 400px;
}

.loading-icon,
.error-icon {
  font-size: 4rem;
  color: var(--text-color-secondary);
  margin-bottom: 1rem;
}

.loading-container h3,
.error-container h3 {
  color: var(--text-color);
  margin-bottom: 0.5rem;
  font-size: 1.5rem;
}

.loading-container p,
.error-container p {
  color: var(--text-color-secondary);
  margin-bottom: 2rem;
}

.error-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.retry-button {
  background: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

.job-details-content {
  margin: 0 auto;
  padding: 2rem;
  display: grid;
  grid-template-columns: 1fr 350px;
  gap: 2rem;
  display: block;
}

.job-main {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.job-header-card {
  background: var(--surface-card);
  border: 1px solid var(--surface-border);
  border-radius: 16px;
  padding: 2rem;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 2rem;
}

.job-header-content {
  display: flex;
  gap: 1.5rem;
  flex: 1;
}

.job-header-info {
  flex: 1;
}

.job-badges {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.job-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-color);
  margin: 0 0 0.5rem 0;
  line-height: 1.2;
}

.job-company {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--primary-color);
  margin: 0 0 1rem 0;
}

.job-meta {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.job-location,
.job-type,
.job-experience,
.job-posted,
.job-vacancies {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-color-secondary);
  font-size: 0.9rem;
}

.job-location i,
.job-type i,
.job-experience i,
.job-posted i,
.job-vacancies i {
  color: var(--primary-color);
}

.job-salary {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--primary-color);
}

.job-header-actions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: flex-end;
}

.save-button,
.apply-button {
  min-width: 140px;
}

.apply-button {
  background: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
  font-weight: 600 !important;
}

.apply-button:disabled {
  background: var(--green-500) !important;
  border-color: var(--green-500) !important;
  opacity: 1 !important;
}

.job-section {
  background: var(--surface-card);
  border: 1px solid var(--surface-border);
  border-radius: 12px;
  padding: 1.5rem;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0 0 1rem 0;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid var(--surface-border);
}

.section-content {
  color: var(--text-color-secondary);
  line-height: 1.6;
}

.job-description {
  font-size: 1rem;
  margin: 0;
}

.additional-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.info-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: var(--surface-50);
  border-radius: 8px;
}

.info-item i {
  color: var(--primary-color);
  font-size: 1.25rem;
  margin-top: 0.25rem;
}

.info-item strong {
  display: block;
  color: var(--text-color);
  margin-bottom: 0.25rem;
}

.info-item p {
  margin: 0;
  color: var(--text-color-secondary);
  font-size: 0.9rem;
}

.responsibilities-grid {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.responsibility-tag {
  font-size: 0.85rem !important;
  padding: 0.5rem 1rem !important;
  background-color: #e3f2fd !important;
  color: #1976d2 !important;
  border: 1px solid #bbdefb !important;
}

.requirements-grid {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.requirement-tag {
  font-size: 0.85rem !important;
  padding: 0.5rem 1rem !important;
  background-color: #e3f2fd !important;
  color: #1976d2 !important;
  border: 1px solid #bbdefb !important;
}

.skills-grid {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.skill-tag {
  font-size: 0.85rem !important;
  padding: 0.5rem 1rem !important;
  background-color: #e3f2fd !important;
  color: #1976d2 !important;
  border: 1px solid #bbdefb !important;
}

.benefits-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.benefit-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: var(--surface-50);
  border-radius: 8px;
}

.benefit-icon {
  color: var(--green-500);
  font-weight: 600;
}

.job-sidebar {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.sidebar-card {
  background: var(--surface-card);
  border: 1px solid var(--surface-border);
  border-radius: 12px;
  padding: 1.5rem;
}

.sidebar-card h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0 0 1rem 0;
}

.apply-card p {
  color: var(--text-color-secondary);
  margin-bottom: 1.5rem;
}

.apply-button-sidebar {
  width: 100%;
  background: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
  font-weight: 600 !important;
  margin-bottom: 1rem;
}

.apply-button-sidebar:disabled {
  background: var(--green-500) !important;
  border-color: var(--green-500) !important;
  opacity: 1 !important;
}

.apply-stats {
  display: flex;
  justify-content: space-around;
  padding-top: 1rem;
  border-top: 1px solid var(--surface-border);
}

.stat {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
}

.stat-label {
  font-size: 0.8rem;
  color: var(--text-color-secondary);
}

.company-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.company-details h4 {
  margin: 0 0 0.25rem 0;
  color: var(--text-color);
  font-weight: 600;
}

.company-details p {
  margin: 0;
  color: var(--text-color-secondary);
  font-size: 0.9rem;
}

.company-button {
  width: 100%;
}

.details-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid var(--surface-border);
}

.detail-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.detail-label {
  font-weight: 500;
  color: var(--text-color-secondary);
  font-size: 0.9rem;
}

.detail-value {
  font-weight: 600;
  color: var(--text-color);
  font-size: 0.9rem;
}

.share-buttons {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.share-button {
  width: 100%;
  justify-content: flex-start !important;
}

.edit-button {
  background: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
  font-weight: 600 !important;
}

@media (max-width: 768px) {
  .job-details-content {
    grid-template-columns: 1fr;
    gap: 1rem;
    padding: 1rem;
  }

  .job-header-card {
    flex-direction: column;
    align-items: flex-start;
    gap: 1.5rem;
  }

  .job-header-content {
    flex-direction: column;
    gap: 1rem;
  }

  .job-header-actions {
    flex-direction: row;
    align-items: center;
    width: 100%;
  }

  .save-button,
  .apply-button {
    flex: 1;
  }

  .job-title {
    font-size: 2rem;
  }

  .job-company {
    font-size: 1.25rem;
  }

  .job-meta {
    flex-direction: column;
    gap: 0.75rem;
  }

  .additional-info-grid,
  .benefits-grid {
    grid-template-columns: 1fr;
  }

  .error-actions {
    flex-direction: column;
  }
}
</style>