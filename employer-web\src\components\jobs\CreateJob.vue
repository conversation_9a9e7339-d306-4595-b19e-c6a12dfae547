<script setup lang="ts">
import { ref, reactive, watch, onMounted, computed, defineProps, nextTick } from "vue";
import { useRouter } from "vue-router";
// import { JobType, PaymentType, JobStatus, JobUrgency, ExperienceLevel, ContactDisplayType } from '@/constants/enums'
import InputText from "primevue/inputtext";
import Textarea from "primevue/textarea";
import Chips from "primevue/chips";
import InputNumber from "primevue/inputnumber";
import Button from "primevue/button";
import Card from "primevue/card";
import { useI18n } from 'vue-i18n'
import { useToast } from "primevue/usetoast";
import Stepper from "primevue/stepper";
import StepList from "primevue/steplist";
import StepPanels from "primevue/steppanels";
import StepItem from "primevue/stepitem";
import Step from "primevue/step";
import StepPanel from "primevue/steppanel";
import Editor from "primevue/editor";
import Select from "primevue/select";
import axios from "axios";
import httpClient from "@/api/httpClient";
import { ENDPOINTS } from "@/api/endpoints";
import { useAuthStore } from "@/stores/auth";
import ProgressSpinner from "primevue/progressspinner";
import InputSwitch from "primevue/inputswitch";
import FileUpload from "primevue/fileupload";
import { useJobsStore } from '@/stores/jobs';
import Breadcrumb from 'primevue/breadcrumb'

const router = useRouter();
const toast = useToast();
const isSubmitting = ref(false);
const authStore = useAuthStore();
const isLoading = ref(true);
const jobsStore = useJobsStore();
const isJobLoading = ref(false);

const props = defineProps({
  editMode: { type: Boolean, default: false },
  jobId: { type: [String, Number], default: null },
});

const home = ref({
  icon: 'pi pi-home',
  label: 'Home', // optional
  command: () => {
    router.push('/employer/dashboard')
  }
});

const items = ref([
  {
    label: 'Jobs',
    command: () => {
      router.push('/employer/jobs')
    }
  },
  {
    label: 'Post a Job',
    to: '/',
    disabled: true
  }
]);

const { t } = useI18n()

// Initialize job enums
const jobEnums = ref({
  jobTypes: [],
  paymentTypes: [],
  jobUrgencies: [],
  experienceLevels: [],
  contactTypes: [],
  urgencyLevels: [],
});

const industries = ref([]);
const subIndustries = ref([]);

// Watch for enums to become available
watch(
  () => authStore.enums,
  (newEnums) => {
    if (newEnums) {
      console.log("Enums available:", newEnums); // Debug log
      jobEnums.value = {
        jobTypes: newEnums.jobTypes || [],
        paymentTypes: newEnums.paymentTypes || [],
        jobUrgencies: newEnums.jobUrgencies || [],
        experienceLevels: newEnums.experienceLevels || [],
        contactTypes: newEnums.contactDisplayTypes || [],
        urgencyLevels: newEnums.urgencyLevels || [],
      };
      isLoading.value = false;
    }
  },
  { immediate: true, deep: true }
);

// Watch for industries to become available
watch(
  () => authStore.industries,
  (newIndustries) => {
    if (newIndustries) {
      console.log("Industries available:", newIndustries); // Debug log
      industries.value = newIndustries;
      isLoading.value = false;
    }
  },
  { immediate: true, deep: true }
);

// Watch for industry changes to update sub-industries

// Add computed property for salary display based on payment type
const salaryDisplay = computed(() => {
  if (!job.paymentType) return "";

  const paymentTypeMap = {
    DAILY: 5000,
    WEEKLY: 50000,
    BI_WEEKLY: 100000,
    MONTHLY: 500000,
  };
  const amount = paymentTypeMap[job.paymentType];
  return amount ? `₹${amount.toLocaleString()}` : "";
});

const job = reactive({
  title: "",
  description: "",
  industryId: "",
  subIndustryId: "",
  salary: 0,
  jobType: null,
  paymentType: null,
  location: "",
  urgency: null,
  experienceLevel: null,
  benefits: [],
  requirements: [],
  responsibilities: [],
  skills: [],
  contactDisplayType: null,
  contactPhone: "",
  contactEmail: "",
  contactPerson: "",
  vacancies: null,
  workingHours: null,
  accommodation: false,
  transportation: false,
  foodProvided: false,
  safetyEquipment: false,
  trainingProvided: false,
  showContact: true,
  thumbnail: "",
  images: [],
});

// Change phoneInput default to empty string (only digits entered)
const phoneInput = ref("");

// Update onPhoneBlur to combine '+91' and phoneInput for job.contactPhone
const onPhoneBlur = () => {
  if (phoneInput.value) {
    job.contactPhone = '+91 ' + phoneInput.value;
  } else {
    job.contactPhone = '';
  }
  validateStep4();
};

// Update isValidPhoneNumber to expect '+91 ' + 10 digits
const isValidPhoneNumber = (phone: string) => {
  const phoneRegex = /^\+91\s[0-9]{10}$/;
  return phoneRegex.test(phone.trim());
};

// When switching contact type, clear phoneInput as needed and validate
watch(() => job.contactDisplayType, (newType) => {
  if (newType === 'NONE') {
    job.contactPhone = '';
    job.contactEmail = '';
    phoneInput.value = '';
    errors.contactPhone = '';
    errors.contactEmail = '';
  } else if (newType === 'PHONE') {
    job.contactEmail = '';
    errors.contactEmail = '';
  } else if (newType === 'EMAIL') {
    job.contactPhone = '';
    phoneInput.value = '';
    errors.contactPhone = '';
  }

  // Validate step 4 when contact type changes
  validateStep4();
});

// Computed properties for contact type logic
const isPhoneEnabled = computed(() => {
  return job.contactDisplayType === 'PHONE' || job.contactDisplayType === 'BOTH';
});

const isEmailEnabled = computed(() => {
  return job.contactDisplayType === 'EMAIL' || job.contactDisplayType === 'BOTH';
});

const isContactRequired = computed(() => {
  return job.contactDisplayType && job.contactDisplayType !== 'NONE';
});

const errors = reactive({
  title: "",
  description: "",
  industryId: "",
  subIndustryId: "",
  jobType: "",
  experienceLevel: "",
  location: "",
  contactDisplayType: "",
  contactPhone: "",
  contactEmail: "",
  paymentType: "",
  salary: "",
  urgency: "",
});

watch(
  () => job.industryId,
  (newIndustryId) => {
    if (newIndustryId) {
      const selectedIndustry = industries.value.find(
        (ind) => ind.id === newIndustryId
      );
      if (selectedIndustry) {
        subIndustries.value = selectedIndustry.subIndustries || [];
        // Reset sub-industry selection when industry changes
        job.subIndustryId = null;
      }
    } else {
      subIndustries.value = [];
      job.subIndustryId = null;
    }
  }
);

function stripHtmlTags(html) {
  const stripped = html.replace(/<[^>]*>/g, '');
  const textarea = document.createElement('textarea');
  textarea.innerHTML = stripped;
  return textarea.value.trim();
}


const validateStep1 = () => {
  let isValid = true;

  // Title validation
  if (!job.title.trim()) {
    errors.title = "Title is required";
    isValid = false;
  } else {
    errors.title = "";
  }

  // Description validation
  if (!job.description.trim()) {
    errors.description = "Description is required";
    isValid = false;
  } else {
    errors.description = "";
  }

  // Industry validation
  if (!job.industryId) {
    errors.industryId = "Industry is required";
    isValid = false;
  } else {
    errors.industryId = "";
  }

  // Sub-industry validation
  if (!job.subIndustryId) {
    errors.subIndustryId = "Sub-industry is required";
    isValid = false;
  } else {
    errors.subIndustryId = "";
  }

  // Job Type validation
  if (!job.jobType) {
    errors.jobType = "Job type is required";
    isValid = false;
  } else {
    errors.jobType = "";
  }

  // Experience Level validation
  if (!job.experienceLevel) {
    errors.experienceLevel = "Experience level is required";
    isValid = false;
  } else {
    errors.experienceLevel = "";
  }

  // Location validation
  if (!job.location.trim()) {
    errors.location = "Location is required";
    isValid = false;
  } else {
    errors.location = "";
  }

  return isValid;
};

const handleNextStep = (
  currentStep: string,
  nextStep: string,
  activateCallback: (step: string) => void
) => {
  console.log("handleNextStep called:", { currentStep, nextStep });

  if (currentStep === "1" && !validateStep1()) {
    console.log("Step 1 validation failed");
    toast.add({
      severity: "error",
      summary: "Validation Error",
      detail: "Please fill in all required fields",
      life: 3000,
    });
    return;
  }

  if (currentStep === "2" && !validateStep2()) {
    console.log("Step 2 validation failed");
    toast.add({
      severity: "error",
      summary: "Validation Error",
      detail: "Please fill in all required fields correctly",
      life: 3000,
    });
    return;
  }

  // Remove step 4 validation from navigation - only validate when on step 4 or submitting

  // Add validation for other steps here when needed
  activateCallback(nextStep);
};

const validateStep2 = () => {
  let isValid = true;

  // Payment type validation
  if (!job.paymentType) {
    errors.paymentType = "Payment type is required";
    isValid = false;
  } else {
    errors.paymentType = "";
  }

  const paymentTypeMap = {
    DAILY: 5000,
    WEEKLY: 50000,
    BI_WEEKLY: 100000,
    MONTHLY: 500000,
  };

  // Salary validation
  if (!job.salary) {
    errors.salary = "Salary is required";
    isValid = false;
  } else if (
    job.paymentType &&
    paymentTypeMap[job.paymentType] &&
    job.salary > paymentTypeMap[job.paymentType]
  ) {
    errors.salary = `Maximum salary for ${job.paymentType.toLowerCase()} payment is ${salaryDisplay.value
      }`;
    isValid = false;
  } else {
    errors.salary = "";
  }

  // Urgency validation
  if (!job.urgency) {
    errors.urgency = "Urgency is required";
    isValid = false;
  } else {
    errors.urgency = "";
  }

  return isValid;
};

const validateStep4 = () => {
  let isValid = true;

  // Contact type validation
  if (!job.contactDisplayType) {
    errors.contactDisplayType = "Contact type is required";
    isValid = false;
  } else {
    errors.contactDisplayType = "";
  }

  // Validate based on contact display type
  if (job.contactDisplayType === 'NONE') {
    // No validation needed for none
    errors.contactPhone = "";
    errors.contactEmail = "";
    return isValid;
  }

  if (job.contactDisplayType === 'PHONE' || job.contactDisplayType === 'BOTH') {
    // Phone is required when PHONE or BOTH is selected
    if (!job.contactPhone || !job.contactPhone.trim()) {
      errors.contactPhone = "Phone number is required";
      isValid = false;
    } else if (!isValidPhoneNumber(job.contactPhone)) {
      errors.contactPhone = "Please enter a valid phone number";
      isValid = false;
    } else {
      errors.contactPhone = "";
    }
  }

  if (job.contactDisplayType === 'EMAIL' || job.contactDisplayType === 'BOTH') {
    // Email is required when EMAIL or BOTH is selected
    if (!job.contactEmail || !job.contactEmail.trim()) {
      errors.contactEmail = "Email address is required";
      isValid = false;
    } else if (!isValidEmail(job.contactEmail)) {
      errors.contactEmail = "Please enter a valid email address";
      isValid = false;
    } else {
      errors.contactEmail = "";
    }
  }

  return isValid;
};

// Add validation helper functions
const isValidEmail = (email: string) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// Fetch job details if in edit mode
async function fetchAndPrefillJob() {
  if (props.editMode && props.jobId) {
    isJobLoading.value = true;
    try {
      const jobData = await jobsStore.fetchJobById(props.jobId);

      // Set industryId first
      job.industryId = jobData.industry?.id || jobData.industryId || '';

      // Load sub-industries for the selected industry and wait for it to complete
      if (job.industryId) {
        const selectedIndustry = industries.value.find((ind) => ind.id === job.industryId);
        if (selectedIndustry) {
          subIndustries.value = selectedIndustry.subIndustries || [];
          // Use nextTick to ensure DOM updates are complete before setting subIndustryId
          await nextTick();
          // Now set the subIndustryId after sub-industries are loaded
          job.subIndustryId = jobData.subIndustry?.id || jobData.subIndustryId || '';
        }
      } else {
        subIndustries.value = [];
        job.subIndustryId = '';
      }

      // Set other fields
      Object.assign(job, {
        title: jobData.title || '',
        description: jobData.description || '',
        salary: parseFloat(jobData.salary) || 0,
        jobType: jobData.jobType || null,
        paymentType: jobData.paymentType || null,
        location: jobData.location || '',
        urgency: jobData.urgency || null,
        experienceLevel: jobData.experienceLevel || null,
        benefits: jobData.benefits || [],
        requirements: jobData.requirements || [],
        responsibilities: jobData.responsibilities || [],
        skills: jobData.skills || [],
        contactDisplayType: jobData.contactDisplayType || null,
        contactPhone: jobData.contactPhone || '',
        contactEmail: jobData.contactEmail || '',
        contactPerson: jobData.contactPerson || '',
        vacancies: jobData.vacancies || null,
        workingHours: jobData.workingHours || null,
        accommodation: jobData.accommodation || false,
        transportation: jobData.transportation || false,
        foodProvided: jobData.foodProvided || false,
        safetyEquipment: jobData.safetyEquipment || false,
        trainingProvided: jobData.trainingProvided || false,
        showContact: jobData.showContact !== false,
        thumbnail: jobData.thumbnail || '',
        images: jobData.images || [],
      });

      if (jobData.contactPhone) {
        // Extract digits after '+91 '
        const match = jobData.contactPhone.match(/^\+91\s([0-9]{10})$/);
        phoneInput.value = match ? match[1] : '';
      }

    } catch (e) {
      toast.add({ severity: 'error', summary: 'Error', detail: 'Failed to load job data', life: 3000 });
      router.push('/employer/jobs');
    } finally {
      isJobLoading.value = false;
    }
  }
}
onMounted(() => { fetchAndPrefillJob(); });

// In submit function, use update if editMode, else create
const submit = async () => {
  try {
    isSubmitting.value = true;
    if (!validateStep1() || !validateStep2() || !validateStep4()) {
      toast.add({ severity: 'error', summary: 'Validation Error', detail: 'Please fill in all required fields correctly', life: 3000 });
      return;
    }
    const jobData = {
      ...job,
      salary: parseFloat(job.salary) || 0,
      vacancies: job.vacancies ? parseInt(job.vacancies) : null,
      workingHours: job.workingHours ? parseFloat(job.workingHours) : null
    };
    if (!jobData.contactPhone) delete jobData.contactPhone;
    if (!jobData.contactEmail) delete jobData.contactEmail;
    let response;
    if (props.editMode && props.jobId) {
      response = await httpClient.patch(ENDPOINTS.JOBS.UPDATE(props.jobId), jobData);
      toast.add({ severity: 'success', summary: 'Success', detail: 'Job updated successfully', life: 3000 });
      router.push(`/employer/jobs/${props.jobId}`);
    } else {
      response = await httpClient.post(ENDPOINTS.JOBS.CREATE, jobData);
      toast.add({ severity: 'success', summary: 'Success', detail: 'Job created successfully', life: 3000 });
      router.push('/employer/jobs');
    }
  } catch (error) {
    toast.add({ severity: 'error', summary: 'Error', detail: error.response?.data?.message || 'Failed to submit job. Please try again.', life: 3000 });
  } finally {
    isSubmitting.value = false;
  }
};

const handleThumbnailUpload = async (event) => {
  if (!props.editMode || !props.jobId) return;
  const file = event.files[0];
  if (!file) return;

  try {
    const response = await uploadService.uploadJobThumbnail(props.jobId, file);
    if (response.success) {
      job.thumbnail = response.data.thumbnail;
      toast.add({ severity: 'success', summary: 'Success', detail: 'Thumbnail uploaded', life: 3000 });
    }
  } catch (error) {
    toast.add({ severity: 'error', summary: 'Error', detail: error.message, life: 3000 });
  }
};

const handleImagesUpload = async (event) => {
  if (!props.editMode || !props.jobId) return;
  const files = event.files;
  if (!files.length) return;

  try {
    const uploadPromises = files.map(file => uploadService.uploadJobImage(props.jobId, file));
    const responses = await Promise.all(uploadPromises);

    const successfulUploads = responses.filter(r => r.success);
    if (successfulUploads.length > 0) {
      // Assuming the response for each image upload returns the updated job object or image list
      // For simplicity, I'll just refetch the job data to get the updated images
      await fetchAndPrefillJob();
      toast.add({ severity: 'success', summary: 'Success', detail: `${successfulUploads.length} image(s) uploaded`, life: 3000 });
    }

    const failedUploads = responses.length - successfulUploads.length;
    if (failedUploads > 0) {
      toast.add({ severity: 'warn', summary: 'Some uploads failed', detail: `${failedUploads} image(s) could not be uploaded`, life: 3000 });
    }
  } catch (error) {
    toast.add({ severity: 'error', summary: 'Error', detail: 'An unexpected error occurred during image uploads.', life: 3000 });
  }
};
</script>

<template>
  <div class="create-job-page flex-1 overflow-x-hidden overflow-y-auto">
    <div class="create-job-content">
      <div class="job-main">
        <Card class="job-form-card">
          <template #title>
            <h1 class="page-title">{{ editMode ? 'Edit Job' : 'Post a Job' }}</h1>
          </template>
          <template #content>
            <div v-if="isLoading || isJobLoading" class="loading-container">
              <ProgressSpinner />
              <span>Please wait...</span>
            </div>
            <Stepper v-else class="job-stepper" value="1">
              <!-- Step 1: Basic Information -->
              <StepItem value="1">
                <Step>Basic Information</Step>
                <StepPanel v-slot="{ activateCallback }">
                  <div class="step-content">
                    <div class="form-section">
                      <div class="form-content">
                        <div class="form-field">
                          <label>Job Title <span class="required">*</span></label>
                          <InputText v-model="job.title" placeholder="Enter job title" class="w-full"
                            :class="{ 'p-invalid': errors.title }" @blur="validateStep1" />
                          <small class="p-error" v-if="errors.title">{{
                            errors.title
                          }}</small>
                        </div>

                        <div class="form-field">
                          <label>Description
                            <span class="required">*</span></label>
                          <Editor v-model="job.description" editorStyle="height: 200px"
                            :class="{ 'p-invalid': errors.description }" @blur="validateStep1">
                            <template v-slot:toolbar>
                              <span class="ql-formats">
                                <button v-tooltip.bottom="'Bold'" class="ql-bold"></button>
                                <button v-tooltip.bottom="'Italic'" class="ql-italic"></button>
                                <button v-tooltip.bottom="'Underline'" class="ql-underline"></button>
                              </span>
                            </template>
                          </Editor>
                          <small class="p-error" v-if="errors.description">{{
                            errors.description
                          }}</small>
                        </div>

                        <div class="form-field">
                          <label>Industry <span class="required">*</span></label>
                          <Select v-model="job.industryId" :options="industries" optionLabel="name" optionValue="id"
                            placeholder="Select industry" :class="{ 'p-invalid': errors.industryId }"
                            @change="validateStep1" />
                          <small class="p-error" v-if="errors.industryId">{{
                            errors.industryId
                          }}</small>
                        </div>

                        <div class="form-field">
                          <label>Industry sub-category<span class="required">*</span></label>
                          <Select v-model="job.subIndustryId" :options="subIndustries" optionLabel="name"
                            optionValue="id" placeholder="Select sub-industry"
                            :class="{ 'p-invalid': errors.subIndustryId }" @change="validateStep1"
                            :disabled="!job.industryId" />
                          <small class="p-error" v-if="errors.subIndustryId">{{ errors.subIndustryId }}</small>
                        </div>

                        <div class="form-field">
                          <label>Location <span class="required">*</span></label>
                          <InputText v-model="job.location" placeholder="Enter location" class="w-full"
                            :class="{ 'p-invalid': errors.location }" @blur="validateStep1" />
                          <small class="p-error" v-if="errors.location">{{
                            errors.location
                          }}</small>
                        </div>

                        <div class="form-field">
                          <label>Job Type <span class="required">*</span></label>
                          <Select v-model="job.jobType" :options="jobEnums.jobTypes" optionLabel="label"
                            optionValue="id" placeholder="Select job type" :class="{ 'p-invalid': errors.jobType }"
                            @change="validateStep1" />
                          <small class="p-error" v-if="errors.jobType">{{
                            errors.jobType
                          }}</small>
                        </div>

                        <div class="form-field">
                          <label>Experience Level
                            <span class="required">*</span></label>
                          <Select v-model="job.experienceLevel" :options="jobEnums.experienceLevels" optionLabel="label"
                            optionValue="id" placeholder="Select experience level"
                            :class="{ 'p-invalid': errors.experienceLevel }" @change="validateStep1" />
                          <small class="p-error" v-if="errors.experienceLevel">{{ errors.experienceLevel }}</small>
                        </div>

                        <div class="form-field">
                          <label>Vacancies</label>
                          <InputNumber v-model="job.vacancies" class="w-full" placeholder="Enter number of vacancies" />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="step-actions">
                    <Button label="Next" @click="handleNextStep('1', '2', activateCallback)" />
                  </div>
                </StepPanel>
              </StepItem>

              <!-- Step 2: Compensation & Requirements -->
              <StepItem value="2">
                <Step>Compensation & Requirements</Step>
                <StepPanel v-slot="{ activateCallback }">
                  <div class="step-content">
                    <div class="form-section">
                      <div class="form-content">
                        <div class="form-field">
                          <label>Payment Type
                            <span class="required">*</span></label>
                          <Select v-model="job.paymentType" :options="jobEnums.paymentTypes" optionLabel="label"
                            optionValue="id" placeholder="Select payment type" class="w-full"
                            :class="{ 'p-invalid': errors.paymentType }" @change="validateStep2" />
                          <small class="p-error" v-if="errors.paymentType">{{
                            errors.paymentType
                          }}</small>
                        </div>

                        <div class="form-field">
                          <label>Salary <span class="required">*</span></label>
                          <InputNumber v-model="job.salary" class="w-full" placeholder="Enter salary amount" prefix="₹"
                            :class="{ 'p-invalid': errors.salary }" @blur="validateStep2" />
                          <small class="p-error" v-if="errors.salary">{{
                            errors.salary
                          }}</small>
                          <small v-if="salaryDisplay" class="salary-hint">Maximum: {{ salaryDisplay }}</small>
                        </div>

                        <div class="form-field">
                          <label>Urgency <span class="required">*</span></label>
                          <Select v-model="job.urgency" :options="jobEnums.urgencyLevels" optionLabel="label"
                            optionValue="id" placeholder="Select urgency" class="w-full"
                            :class="{ 'p-invalid': errors.urgency }" @change="validateStep2" />
                          <small class="p-error" v-if="errors.urgency">{{
                            errors.urgency
                          }}</small>
                        </div>

                        <div class="form-field">
                          <label>Requirements</label>
                          <Chips v-model="job.requirements" separator="," class="w-full" />
                        </div>

                        <div class="form-field">
                          <label>Responsibilities</label>
                          <Chips v-model="job.responsibilities" separator="," class="w-full" />
                        </div>

                        <div class="form-field">
                          <label>Skills</label>
                          <Chips v-model="job.skills" separator="," class="w-full" />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="step-actions">
                    <Button label="Back" severity="secondary" @click="activateCallback('1')" />
                    <Button label="Next" @click="handleNextStep('2', '3', activateCallback)" />
                  </div>
                </StepPanel>
              </StepItem>

              <!-- Step 3: Benefits & Additional Information -->
              <StepItem value="3">
                <Step>Benefits & Additional Information</Step>
                <StepPanel v-slot="{ activateCallback }">
                  <div class="step-content">
                    <div class="form-section">
                      <div class="form-content">
                        <div class="form-field">
                          <label>Benefits</label>
                          <Chips v-model="job.benefits" separator="," class="w-full" />
                        </div>

                        <div class="form-field">
                          <label>Working Hours</label>
                          <InputNumber v-model="job.workingHours" placeholder="Enter working hours" class="w-full" />
                        </div>

                        <div class="form-field">
                          <label>Accommodation Provided</label>
                          <InputSwitch v-model="job.accommodation" />
                        </div>

                        <div class="form-field">
                          <label>Transportation Provided</label>
                          <InputSwitch v-model="job.transportation" />
                        </div>

                        <div class="form-field">
                          <label>Food Provided</label>
                          <InputSwitch v-model="job.foodProvided" />
                        </div>

                        <div class="form-field">
                          <label>Safety Equipment Provided</label>
                          <InputSwitch v-model="job.safetyEquipment" />
                        </div>

                        <div class="form-field">
                          <label>Training Provided</label>
                          <InputSwitch v-model="job.trainingProvided" />
                        </div>

                   <!--<div class="form-field">
                          <label>Show Contact Info</label>
                          <InputSwitch v-model="job.showContact" />
                        </div>

                        <div class="form-field">
                          <label>Thumbnail</label>
                          <FileUpload mode="basic" name="thumbnail" :customUpload="true"
                            @uploader="handleThumbnailUpload" :auto="true" accept="image/*" :disabled="!editMode" />
                          <small v-if="!editMode" class="p-error">Save the job to upload a thumbnail.</small>
                        </div>

                        <div class="form-field">
                          <label>Images</label>
                          <FileUpload name="images[]" :customUpload="true" @uploader="handleImagesUpload"
                            :multiple="true" :auto="true" accept="image/*" :disabled="!editMode" />
                          <small v-if="!editMode" class="p-error">Save the job to upload images.</small>
                        </div>-->
                      </div>
                    </div>
                  </div>
                  <div class="step-actions">
                    <Button label="Back" severity="secondary" @click="activateCallback('2')" />
                    <Button label="Next" @click="handleNextStep('3', '4', activateCallback)" />
                  </div>
                </StepPanel>
              </StepItem>

              <!-- Step 4: Contact Information -->
              <StepItem value="4">
                <Step>Contact Information</Step>
                <StepPanel v-slot="{ activateCallback }">
                  <div class="step-content">
                    <div class="form-section">
                      <div class="form-content">
                        <div class="form-field">
                          <label>Contact Type
                            <span class="required">*</span></label>
                          <Select v-model="job.contactDisplayType" :options="jobEnums.contactTypes" optionLabel="label"
                            optionValue="id" placeholder="Select contact type" class="w-full"
                            :class="{ 'p-invalid': errors.contactDisplayType }" @change="validateStep4"
                            @blur="validateStep4" />
                          <small class="p-error" v-if="errors.contactDisplayType">{{
                            errors.contactDisplayType
                          }}</small>
                        </div>

                        <div class="form-field">
                          <label>Contact Person</label>
                          <InputText v-model="job.contactPerson" placeholder="Enter contact person name" class="w-full"
                            @blur="validateStep4" />
                        </div>
                        <div class="form-field">
                          <label :class="{ 'required': isPhoneEnabled }">
                            Contact Phone
                            <span v-if="isPhoneEnabled" class="required">*</span>
                          </label>
                          <div style="display: flex; align-items: center; gap: 0.5rem;">
                            <span
                              style="padding: 0.5rem 0.75rem; background: var(--surface-100); border: 1px solid var(--surface-border); border-radius: 4px 0 0 4px; color: var(--text-color-secondary);">+91</span>
                            <InputText v-model="phoneInput" placeholder="Enter 10-digit number" class="w-full"
                              :class="{ 'p-invalid': errors.contactPhone }" :disabled="!isPhoneEnabled" maxlength="10"
                              style="border-radius: 0 4px 4px 0;" @blur="onPhoneBlur" />
                          </div>
                          <small class="p-error" v-if="errors.contactPhone">{{
                            errors.contactPhone
                          }}</small>
                        </div>

                        <div class="form-field">
                          <label :class="{ 'required': isEmailEnabled }">
                            Contact Email
                            <span v-if="isEmailEnabled" class="required">*</span>
                          </label>
                          <InputText v-model="job.contactEmail" placeholder="Enter contact email" class="w-full"
                            :class="{ 'p-invalid': errors.contactEmail }" :disabled="!isEmailEnabled"
                            @blur="validateStep4" />
                          <small class="p-error" v-if="errors.contactEmail">{{
                            errors.contactEmail
                          }}</small>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="step-actions">
                    <Button label="Back" severity="secondary" @click="activateCallback('3')" />
                    <Button :label="editMode ? 'Update Job' : 'Submit'" @click="submit" :loading="isSubmitting"
                      :disabled="isSubmitting" />
                  </div>
                </StepPanel>
              </StepItem>
            </Stepper>
          </template>
        </Card>
      </div>
    </div>
  </div>
</template>

<style scoped>
.create-job-page {
  background: linear-gradient(135deg,
      var(--jobs-bg-start) 0%,
      var(--jobs-bg-end) 100%);
}

.job-main {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  padding: 1rem;
}

.job-form-card {
  background: var(--surface-card);
  border: 1px solid var(--surface-border);
  border-radius: 16px;
  box-shadow:
    0 4px 6px -1px rgb(0 0 0 / 0.1),
    0 2px 4px -2px rgb(0 0 0 / 0.1);
}

.page-title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-color);
  margin: 0;
}

.job-stepper {
  display: flex;
  flex-direction: column;
  height: calc(74vh);
}

.step-content {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
}

.form-section {
  background: var(--surface-card);
  border: 1px solid var(--surface-border);
  border-radius: 12px;
  padding: 1.5rem;
}

.form-content {
  display: grid;
  gap: 1.5rem;
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-field label {
  font-weight: 500;
  color: var(--text-color);
}

.step-actions {
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--surface-card);
  padding: 1rem;
  border-top: 1px solid var(--surface-border);
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  z-index: 10;
}

.p-stepitem.p-stepitem-active {
  overflow: hidden;
  overflow-y: auto;
}

@media (max-width: 768px) {
  .create-job-content {
    padding: 1rem;
  }

  .job-main {
    padding: 0;
  }

  .page-title {
    font-size: 1.75rem;
  }

  .form-section {
    padding: 1rem;
  }

  .step-actions {
    flex-direction: column;
  }

  .step-actions button {
    width: 100%;
  }
}

.required {
  color: var(--red-500);
  margin-left: 2px;
}

.p-error {
  color: var(--red-500);
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  padding: 2rem;
  height: 100%;
}

.p-invalid {
  border-color: var(--red-500) !important;
}

.salary-hint {
  color: var(--primary-color);
  font-size: 0.875rem;
  margin-top: 0.25rem;
  font-style: italic;
}

/* Styles for disabled fields */
.form-field input:disabled,
.form-field .p-inputtext:disabled {
  background-color: var(--surface-100);
  color: var(--text-color-secondary);
  cursor: not-allowed;
  opacity: 0.6;
}

.form-field label.required {
  color: var(--text-color);
}

.form-field label.required .required {
  color: var(--red-500);
  margin-left: 2px;
}

.contact-type-hint {
  color: var(--text-color-secondary);
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.contact-type-hint strong {
  font-weight: 700;
  color: var(--text-color);
}
</style>
