<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useJobsStore } from '@/stores/jobs'
import Select from 'primevue/select'
import Button from 'primevue/button'
import Avatar from 'primevue/avatar'
import Tag from 'primevue/tag'
import SpeedDial from 'primevue/speeddial'
import alertManager from '@/utils/alertManager'

const router = useRouter()
const route = useRoute()
const { t } = useI18n()
const jobsStore = useJobsStore()

// Mobile filter drawer state
const showMobileFilters = ref(false)

// Filters state
const filters = ref({
  search: '',
  location: '',
  industry: null,
  jobType: [],
  experienceLevel: null,
  paymentType: null,
  status: [],
  isUrgent: null,
  isRemote: null,
  minVacancies: null
})

const timeFilter = ref('any'); // Default: any time
const timeOptions = [
  { label: t('jobs.anyTime'), value: 'any' },
  { label: t('jobs.pastMonth'), value: 'month' },
  { label: t('jobs.pastWeek'), value: 'week' },
  { label: t('jobs.past24Hours'), value: '24h' }
];

// Mock saved jobs and applications
const savedJobIds = ref(new Set([1, 5, 12]))
const appliedJobIds = ref(new Set([3, 8, 15]))

const handleFiltersUpdate = (newFilters) => {
  filters.value = newFilters
  jobsStore.setFilters(newFilters)
  jobsStore.setPage(1) // Reset to first page
  jobsStore.fetchJobs()

  // Close mobile drawer after applying filters
  showMobileFilters.value = false
}

const handleClearFilters = () => {
  filters.value = {
    search: '',
    location: '',
    industry: null,
    jobType: [],
    experienceLevel: null,
    paymentType: null,
    status: [],
    isUrgent: null,
    isRemote: null,
    minVacancies: null
  }
  jobsStore.clearFilters()
  jobsStore.fetchJobs()

  // Close mobile drawer after clearing filters
  showMobileFilters.value = false
}

const updateSort = () => {
  // No need to refetch jobs, sorting is local
}

const loadMoreJobs = () => {
  if (jobsStore.pagination.hasNextPage) {
    jobsStore.setPage(jobsStore.pagination.page + 1)
    jobsStore.fetchJobs()
  }
}

// Update formatDate to respect the filter
const formatDate = (dateString) => {
  if (!dateString) return 'just now';
  const date = new Date(dateString);
  const now = new Date();
  const diffTime = Math.abs(now.getTime() - date.getTime());
  const diffSeconds = Math.ceil(diffTime / 1000);
  const diffMinutes = Math.ceil(diffTime / (1000 * 60));
  const diffHours = Math.ceil(diffTime / (1000 * 60 * 60));
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  if (diffSeconds < 60) return 'just now';
  if (diffMinutes < 60) return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} ago`;
  if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
  if (diffDays === 1) return '1 day ago';
  if (diffDays < 7) return `${diffDays} days ago`;
  if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`;
  if (diffDays < 365) return `${Math.floor(diffDays / 30)} months ago`;
  return `${Math.floor(diffDays / 365)} years ago`;
}

const hasAdditionalInfo = (job) => {
  return job.workingHours || job.accommodation || job.transportation || job.foodProvided
}

const getStatusSeverity = (status) => {
  switch (status) {
    case 'ACTIVE': return 'success'
    case 'DRAFT': return 'warning'
    case 'CLOSED': return 'danger'
    case 'PAUSED': return 'secondary'
    default: return 'info'
  }
}

const createNewJob = () => {
  router.push(`/employer/jobs/create`)
  console.log("create new job")
}
const isJobSaved = (jobId) => {
  return savedJobIds.value.has(jobId)
}

const hasApplied = (jobId) => {
  return appliedJobIds.value.has(jobId)
}

const viewJobDetails = (job) => {
  router.push(`/employer/jobs/${job.id}`)
}

const deleteJob = async (job) => {
  const confirmed = await alertManager.showConfirm(
    'Confirm',
    `Are you sure you want to delete the job "${job.title}"?`,
    {
      confirmText: 'Delete',
      cancelText: 'Cancel'
    }
  )

  if (!confirmed) {
    return
  }

  try {
    await jobsStore.deleteJob(job.id)
    await jobsStore.fetchJobs()
    alertManager.showSuccess('Deleted', 'Job deleted successfully')
  } catch (err) {
    alertManager.showError('Error', err.message || 'Failed to delete job')
  }
}

const applyToJob = (job) => {
  if (!hasApplied(job.id)) {
    viewJobDetails(job)
    //appliedJobIds.value.add(job.id)
    //console.log('Apply to job:', job.title)
    // TODO: Implement application flow
  }
}

// Computed sorted jobs (frontend only)
const filteredJobs = computed(() => {
  let jobs = [...jobsStore.jobs];
  if (timeFilter.value === 'any') return jobs;

  const now = new Date();
  return jobs.filter(job => {
    const createdAt = new Date(job.createdAt);
    if (timeFilter.value === 'month') {
      return (now - createdAt) <= 30 * 24 * 60 * 60 * 1000;
    }
    if (timeFilter.value === 'week') {
      return (now - createdAt) <= 7 * 24 * 60 * 60 * 1000;
    }
    if (timeFilter.value === '24h') {
      return (now - createdAt) <= 24 * 60 * 60 * 1000;
    }
    return true;
  });
});

const totalJobs = computed(() => {

})

// Add this computed function for urgency color
const getUrgencySeverity = (urgency) => {
  return urgency === 'URGENT' ? 'danger' : 'info';
}

// Handle URL parameters for category filtering
onMounted(() => {
  if (route.query.category) {
    filters.value.industry = route.query.category
    handleFiltersUpdate(filters.value)
  } else {
    jobsStore.fetchJobs()
  }
})

</script>

<template>
  <div class="jobs-page flex-1 overflow-x-hidden overflow-y-auto">

    <div class="jobs-content">
      <!-- Desktop Filter Panel -->
      <!-- <JobsFilter 
          :filters="filters"
          @update:filters="handleFiltersUpdate"
          @clear-filters="handleClearFilters"
          class="filter-panel desktop-filter"
        /> -->

      <!-- Mobile Filter Drawer -->
      <!-- <Drawer 
          v-model:visible="showMobileFilters" 
          position="left" 
          :header="t('jobs.filters')"
          class="mobile-filter-drawer"
        >
          <JobsFilter 
            :filters="filters"
            @update:filters="handleFiltersUpdate"
            @clear-filters="handleClearFilters"
            class="mobile-filter-content"
          />
          <template #footer>
            <div class="drawer-footer">
              <Button 
                @click="handleClearFilters"
                :label="t('jobs.clearAll')"
                outlined
                severity="secondary"
                class="clear-btn"
              />
              <Button 
                @click="showMobileFilters = false"
                :label="t('common.apply')"
                class="apply-filters-btn"
              />
            </div>
          </template>
</Drawer> -->

      <!-- Jobs List Content -->
      <div class="content-panel">
        <div class="content-header">
          <!-- <Button label="Create New Job" @click="createNewJob()"></Button> -->

          <!-- Mobile Filter Button -->
          <!-- <Button 
              @click="showMobileFilters = true"
              icon="pi pi-filter"
              :label="t('jobs.filters')"
              outlined
              class="mobile-filter-btn"
            /> -->

          <div class="results-info">
            <span class="results-count">{{ t('jobs.jobsFound', { count: jobsStore.pagination?.total ||0 }) }}</span>
            <div class="sort-controls">
              <div class="flex items-center gap-2">
                <label for="time-filter" class="font-medium">{{ t('jobs.filterByTime') }}</label>
                <Select id="time-filter" v-model="timeFilter" :options="timeOptions" optionLabel="label"
                  optionValue="value" :placeholder="t('jobs.anyTime')" class="sort-dropdown" />
              </div>
            </div>
          </div>
        </div>

        <div class="jobs-list" v-if="jobsStore.hasJobs">
          <div v-for="(job, index) in filteredJobs" :key="job.id"
            :class="['job-card', { 'featured': job.isFeatured, 'urgent': job.isUrgent }]">
            <div class="job-header">
              <div class="job-avatar">
                <Avatar :label="job.company?.charAt(0).toUpperCase()" :style="{ backgroundColor: job.color }"
                  shape="circle" size="large" />
              </div>
              <div class="job-basic-info">
                <h3 class="job-title" @click="viewJobDetails(job)">{{ job.title }}</h3>
                <p class="job-company">{{ job.company }}</p>
                <div class="job-badges">
                  <Tag v-if="job.isFeatured" :value="t('jobs.featured')" severity="success" class="featured-badge" />
                  <Tag v-if="job.isUrgent" :value="t('jobs.urgent')" severity="danger" class="urgent-badge" />
                  <Tag v-if="job.urgency" :value="job.urgency" :severity="getUrgencySeverity(job.urgency)" class="urgency-badge" />
                  <Tag :value="job.paymentType" severity="secondary" class="payment-type-badge" />
                </div>
              </div>
              <div class="job-actions">
                <Button icon="pi pi-trash" text size="large" @click="deleteJob(job)" v-tooltip.left="t('common.delete')" />
                <Button icon="pi pi-external-link" text size="large" @click="viewJobDetails(job)"
                  v-tooltip.left="t('common.view') + ' Details'" />
              </div>
            </div>

            <div class="job-content">
              <div class="job-description" v-html="job.description"></div>

              <div class="job-details">
                <div class="job-meta">
                  <span class="job-location">
                    <i class="pi pi-map-marker"></i>
                    {{ job.location }}
                  </span>
                  <span class="job-salary">
                    <i class="pi pi-indian-rupee"></i>
                    {{ job.salary.toLocaleString() }} {{ job.paymentType.toLowerCase() }}
                  </span>
                  <span class="job-posted">
                    <i class="pi pi-calendar"></i>
                    {{ t('jobs.postedAgo', { time: formatDate(job.createdAt) }) }}
                  </span>
                  <span class="job-vacancies">
                    <i class="pi pi-users"></i>
                    {{ job.vacancies }} {{ job.vacancies === 1 ? 'position' : 'positions' }}
                  </span>
                </div>

                <!-- Additional Job Info -->
                <div class="job-additional-info" v-if="hasAdditionalInfo(job)">
                  <div class="info-items">
                    <span v-if="job.workingHours" class="info-item">
                      <i class="pi pi-clock"></i>
                      {{ job.workingHours }}
                    </span>
                    <span v-if="job.accommodation" class="info-item">
                      <i class="pi pi-home"></i>
                      Accommodation
                    </span>
                    <span v-if="job.transportation" class="info-item">
                      <i class="pi pi-car"></i>
                      Transportation
                    </span>
                    <span v-if="job.foodProvided" class="info-item">
                      <i class="pi pi-shopping-cart"></i>
                      Food Provided
                    </span>
                  </div>
                </div>

                <div class="job-skills" v-if="job.skills && job.skills.length > 0">
                  <Tag v-for="skill in job.skills.slice(0, 4)" :key="skill" :value="skill" class="skill-tag" />
                  <span v-if="job.skills.length > 4" class="more-skills">
                    +{{ job.skills.length - 4 }} more
                  </span>
                </div>
              </div>
            </div>

            <div class="job-footer">
              <div class="job-stats">
                <span class="applicants-count">
                  <i class="pi pi-users"></i>
                  {{ t('jobs.applicants', { count: job.applicationsCount || 0 }) }}
                </span>
                <span class="views-count">
                  <i class="pi pi-eye"></i>
                  {{ t('jobs.views', { count: job.viewsCount || 0 }) }}
                </span>
                <span class="job-status">
                  <Tag :value="job.status" :severity="getStatusSeverity(job.status)" class="status-tag" />
                </span>
              </div>
              <!-- <Button 
                  @click="applyToJob(job)"
                  :label="hasApplied(job.id) ? t('jobs.applied') : t('dashboard.apply')"
                  class="apply-button"
                  :disabled="hasApplied(job.id)"
                >
                  <template v-if="hasApplied(job.id)">
                    <i class="pi pi-check"></i>
                    {{ t('jobs.applied') }}
                  </template>
                </Button> -->
            </div>
          </div>
        </div>

        <!-- Load More Button -->
        <div v-if="jobsStore.hasJobs && jobsStore.pagination.hasNextPage" class="load-more-section">
          <Button @click="loadMoreJobs" outlined size="large" :loading="jobsStore.isLoading" class="load-more-btn"
            :label="t('jobs.loadMore')" icon="pi pi-plus" />
        </div>

        <div v-else-if="!jobsStore.pagination.hasNextPage && !jobsStore.isLoading" class="empty-state">
          <i class="pi pi-briefcase empty-icon"></i>
          <h3>{{ t('jobs.noJobsFound') }}</h3>
          <p>{{ t('jobs.adjustFilters') }}</p>
        </div>

        <div v-if="jobsStore.isLoading" class="loading-state">
          <i class="pi pi-spin pi-spinner loading-icon"></i>
          <p>{{ t('jobs.findingOpportunities') }}</p>
        </div>
      </div>
      <SpeedDial
          direction="up"
          :style="{ position: 'absolute', right: '3.5rem', bottom: '3.2rem' }"
          :tooltipOptions="{ position: 'left' }"
          @click="createNewJob"
        >
          <template #button="{ toggleCallback }">
            <Button icon="pi pi-plus" @click="toggleCallback" rounded raised size="large"/>
          </template>
        </SpeedDial>
    </div>
  </div>
</template>

<style scoped>
.jobs-page {
  background: linear-gradient(135deg, var(--jobs-bg-start) 0%, var(--jobs-bg-end) 100%);
  transition: background var(--transition-duration) ease;
  padding: 1rem;
}

.jobs-content {
  display: grid;
  grid-template-columns: 1fr;
}

.content-panel {
  background: var(--surface-card);
  border: 1px solid var(--surface-border);
  border-radius: 12px;
  padding: 1.5rem;
}

.content-header {
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--surface-border);
}

.page-title {
  margin-bottom: 1rem;
}

.page-title h1 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-color);
  margin: 0 0 0.25rem 0;
}

.page-title p {
  color: var(--text-color-primary);
  margin: 0;
  font-size: 0.95rem;
}

.mobile-filter-btn {
  display: none;
  margin-bottom: 1rem;
}

.results-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.results-count {
  font-weight: 600;
  color: var(--text-color);
}

.sort-dropdown {
  min-width: 180px;
}

.jobs-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.job-card {
  padding: 1.5rem;
  border: 1px solid var(--surface-border);
  border-radius: 12px;
  transition: all var(--transition-duration) ease;
  background: var(--surface-0);
}

.job-card:hover {
  border-color: var(--primary-color);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.job-card.featured {
  border-color: var(--green-500);
  background: linear-gradient(135deg, var(--surface-0) 0%, rgba(34, 197, 94, 0.02) 100%);
}

.job-card.urgent {
  border-color: var(--red-500);
  background: linear-gradient(135deg, var(--surface-0) 0%, rgba(239, 68, 68, 0.02) 100%);
}

.job-header {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1rem;
}

.job-basic-info {
  flex: 1;
  min-width: 0;
}

.job-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 0.25rem;
  cursor: pointer;
}

.job-title:hover {
  color: var(--primary-color);
}

.job-company {
  color: var(--primary-color);
  font-weight: 500;
  margin-bottom: 0.75rem;
  font-size: 1rem;
}

.job-badges {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.urgency-badge {
  border-radius: 4px;
  padding: 0.25rem 0.5rem;
  font-weight: 500;
}

.job-actions {
  display: flex;
  gap: 0.25rem;
}

.job-actions .saved {
  color: var(--red-500) !important;
}

.job-content {
  margin-bottom: 1rem;
}

.job-description {
  color: var(--text-color-primary);
  font-size: 1.1rem;
  margin-bottom: 1rem;
  line-height: 1.5;
  word-break: break-word;
  white-space: pre-line;
}

.job-details {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.job-meta {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.job-location,
.job-salary,
.job-posted,
.job-vacancies {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: var(--text-color-primary);
  font-size: 1rem;
}

.job-location i,
.job-posted i,
.job-vacancies i {
  color: var(--primary-color);
  font-size: 1rem;
}

.job-salary {
  font-weight: 600;
  color: var(--primary-color);
}

.job-salary i {
  color: var(--primary-color);
  font-size: 0.75rem;
}

.job-additional-info {
  margin: 0.75rem 0;
}

.info-items {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 1rem;
  color: var(--text-color-primary);
  background: var(--surface-100);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.info-item i {
  color: var(--primary-color);
  font-size: 1rem;
}

.job-skills {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.skill-tag {
  font-size: 1rem !important;
  background: var(--surface-100) !important;
  color: var(--text-color-primary) !important;
}

.more-skills {
  font-size: 1.1rem;
  color: var(--text-color-primary);
  font-style: italic;
}

.job-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 1rem;
  border-top: 1px solid var(--surface-border);
}

.job-stats {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.applicants-count,
.views-count {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: var(--text-color-primary);
  font-size: 1rem;
}

.applicants-count i,
.views-count i {
  color: var(--primary-color);
  font-size:1rem;
}

.job-status {
  margin-left: auto;
}

.apply-button {
  background: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
  font-weight: 600 !important;
}

.apply-button:disabled {
  background: var(--green-500) !important;
  border-color: var(--green-500) !important;
  opacity: 1 !important;
}

.load-more-section {
  display: flex;
  justify-content: center;
  padding: 2rem 0;
}

.load-more-btn {
  min-width: 200px;
}

.empty-state,
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
}

.empty-icon,
.loading-icon {
  font-size: 3rem;
  color: var(--text-color-primary);
  margin-bottom: 1rem;
}

.empty-state h3 {
  color: var(--text-color);
  margin-bottom: 0.5rem;
}

.empty-state p {
  color: var(--text-color-primary);
  margin-bottom: 1.5rem;
}

/* Mobile Filter Drawer Styles */
.mobile-filter-drawer {
  width: 90vw !important;
  max-width: 400px !important;
}

.mobile-filter-content {
  background: transparent !important;
  border: none !important;
  padding: 0 !important;
  position: static !important;
  height: auto !important;
  max-height: none !important;
  overflow: visible !important;
}

.drawer-footer {
  display: flex;
  gap: 1rem;
  padding: 1rem 0 0 0;
  border-top: 1px solid var(--surface-border);
  margin-top: 1rem;
}

.clear-btn {
  flex: 1;
}

.apply-filters-btn {
  flex: 2;
  background: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

@media (max-width: 1200px) {
  .jobs-content {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

@media (max-width: 768px) {
  .mobile-filter-btn {
    display: flex !important;
  }

  .jobs-content {
    grid-template-columns: 1fr;
    gap: 1rem;
    padding: 1rem;
  }

  .desktop-filter {
    display: none;
  }

  .job-header {
    align-items: flex-start;
    gap: 1rem;
  }

  .job-actions {
    align-self: flex-end;
  }

  .job-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .job-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .apply-button {
    width: 100%;
  }

  .sort-dropdown {
    min-width: 140px;
  }
}

@media (max-width: 480px) {
  .mobile-filter-drawer {
    width: 95vw !important;
  }

  .drawer-footer {
    flex-direction: column;
  }

  .clear-btn,
  .apply-filters-btn {
    flex: none;
  }
}
</style>