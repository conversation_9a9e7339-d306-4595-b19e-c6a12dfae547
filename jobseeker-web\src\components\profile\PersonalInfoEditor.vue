<template>
  <Dialog 
    v-model:visible="localVisible" 
    :header="isEditing ? 'Edit Personal Information' : 'Personal Information'"
    :modal="true"
    :closable="true"
    class="personal-info-dialog"
    :style="{ width: '600px' }"
    @hide="handleCancel"
  >
    <form @submit.prevent="handleSubmit" class="personal-info-form">
      <div class="form-grid">
        <div class="form-group">
          <label for="firstName">First Name *</label>
          <InputText
            id="firstName"
            v-model="formData.firstName"
            :class="{ 'p-invalid': errors.firstName }"
            placeholder="Enter your first name"
          />
          <small v-if="errors.firstName" class="p-error">{{ errors.firstName }}</small>
        </div>

        <div class="form-group">
          <label for="lastName">Last Name *</label>
          <InputText
            id="lastName"
            v-model="formData.lastName"
            :class="{ 'p-invalid': errors.lastName }"
            placeholder="Enter your last name"
          />
          <small v-if="errors.lastName" class="p-error">{{ errors.lastName }}</small>
        </div>

        <div class="form-group">
          <label for="email">Email *</label>
          <InputText
            id="email"
            v-model="formData.email"
            type="email"
            :class="{ 'p-invalid': errors.email }"
            placeholder="Enter your email"
            disabled
          />
          <small class="form-help">Email cannot be changed</small>
        </div>

        <div class="form-group">
          <label for="phone">Phone Number</label>
          <InputText
            id="phone"
            v-model="formData.phone"
            :class="{ 'p-invalid': errors.phone }"
            placeholder="e.g., +919021708017"
          />
          <small v-if="errors.phone" class="p-error">{{ errors.phone }}</small>
        </div>

        <div class="form-group">
          <label for="dateOfBirth">Date of Birth</label>
          <Calendar
            id="dateOfBirth"
            v-model="formData.dateOfBirth"
             :showIcon="true"
            :maxDate="maxDate"
          dateFormat="dd/mm/yy"
            placeholder="Select date"
          />
          <small v-if="errors.dateOfBirth" class="p-error">{{ errors.dateOfBirth }}</small>
        </div>
      </div>
      <!-- Map and address form for personal info -->
      <Location v-model="locationData" :errors="errors" />
    </form>

    <template #footer>
      <div class="dialog-footer">
        <Button 
          @click="handleCancel"
          label="Cancel"
          outlined
          :disabled="isSaving"
        />
        <Button 
          @click="handleSubmit"
          label="Save Changes"
          :loading="isSaving"
          class="save-btn"
          :disabled="isSaving"
        />
      </div>
    </template>
  </Dialog>
</template>

<script setup>
import { ref, watch, computed } from 'vue'
import Dialog from 'primevue/dialog'
import InputText from 'primevue/inputtext'
import Calendar from 'primevue/calendar'
import Button from 'primevue/button'
import alertManager from '@/utils/alertManager'
import Location from './Location.vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  profileData: {
    type: Object,
    default: () => ({})
  },
  isEditing: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:visible', 'save'])

const isSaving = ref(false)
const errors = ref({})

const formData = ref({
  firstName: '',
  lastName: '',
  email: '',
  phone: '',
  dateOfBirth: null
})

const locationData = ref({
  address: '',
  city: '',
  state: '',
  country: '',
  postalCode: '',
  latitude: null,
  longitude: null
})

// Create a computed property for two-way binding with the visible prop
const localVisible = computed({
  get() {
    return props.visible
  },
  set(value) {
    emit('update:visible', value)
  }
})

const maxDate = computed(() => {
  const date = new Date()
  date.setFullYear(date.getFullYear() - 16) // Minimum age 16
  return date
})

// Watch for prop changes
watch(() => props.profileData, (newData) => {
  if (newData) {
    formData.value = {
      firstName: newData.firstName || '',
      lastName: newData.lastName || '',
      email: newData.email || '',
      phone: newData.phone || '',
      dateOfBirth: newData.dateOfBirth ? new Date(newData.dateOfBirth) : null
    }
    locationData.value = {
      address: newData.address || '',
      city: newData.city || '',
      state: newData.state || '',
      country: newData.country || 'India',
      postalCode: newData.postalCode || '',
      latitude: newData.latitude || null,
      longitude: newData.longitude || null
    }
  }
}, { immediate: true, deep: true })

const validateForm = () => {
  errors.value = {}
  
  if (!formData.value.firstName?.trim()) {
    errors.value.firstName = 'First name is required'
  }
  
  if (!formData.value.lastName?.trim()) {
    errors.value.lastName = 'Last name is required'
  }
  
  if (!formData.value.email?.trim()) {
    errors.value.email = 'Email is required'
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.value.email)) {
    errors.value.email = 'Please enter a valid email address'
  }
  
  // Phone validation: Only allow numbers that start with +91 followed by exactly 10 digits
  if (formData.value.phone) {
    const phone = formData.value.phone.replace(/\s/g, '')
    if (!/^\+91[6-9][0-9]{9}$/.test(phone)) {
      errors.value.phone = 'Phone number must start with +91 and be followed by exactly 10 digits (e.g., +919876543210)'
    }
  }

  // Location validation
  // City, State, Country: alphabets only (allow spaces, hyphens)
  if (locationData.value.city && !/^[A-Za-z\s-]+$/.test(locationData.value.city)) {
    errors.value.city = 'City should contain only letters'
  }
  if (locationData.value.state && !/^[A-Za-z\s-]+$/.test(locationData.value.state)) {
    errors.value.state = 'State should contain only letters'
  }
  if (locationData.value.country && !/^[A-Za-z\s-]+$/.test(locationData.value.country)) {
    errors.value.country = 'Country should contain only letters'
  }
  // Postal code: numbers only
  if (locationData.value.postalCode && !/^\d+$/.test(locationData.value.postalCode)) {
    errors.value.postalCode = 'Postal code should contain only numbers'
  }

  return Object.keys(errors.value).length === 0
}

const handleSubmit = async () => {
  if (!validateForm()) {
    return
  }
  
  isSaving.value = true
  
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const updatedData = {
      ...formData.value,
      ...locationData.value,
      dateOfBirth: formData.value.dateOfBirth ? formData.value.dateOfBirth.toISOString().split('T')[0] : null
    }
    
    emit('save', updatedData)
    emit('update:visible', false)
    
   
  } catch (error) {
    alertManager.showError('Error', 'Failed to update personal information. Please try again.')
  } finally {
    isSaving.value = false
  }
}

const handleCancel = () => {
  emit('update:visible', false)
  errors.value = {}
}
</script>

<style scoped>
.personal-info-dialog {
  border-radius: 12px !important;
}

.personal-info-form {
  padding: 1rem 0;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 600;
  color: var(--text-color);
  font-size: 0.9rem;
}

.form-group input,
.form-group .p-calendar {
  width: 100% !important;
}

.form-help {
  font-size: 0.8rem;
  color: var(--text-color-secondary);
}

.dialog-footer {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.save-btn {
  background: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
  }
  
  .dialog-footer {
    flex-direction: column;
  }
}

.p-error {
  color: #e53935 !important;
  font-size: 0.85rem;
  margin-top: 2px;
}
</style>