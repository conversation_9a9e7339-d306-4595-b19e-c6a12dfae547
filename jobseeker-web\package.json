{"name": "jc-web", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "test:unit": "vitest", "test:e2e": "playwright test", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"@primeuix/themes": "^1.1.1", "@primevue/forms": "^4.3.5", "axios": "^1.9.0", "dayjs": "^1.11.13", "leaflet": "^1.9.4", "pinia": "^3.0.1", "primeicons": "^7.0.0", "primevue": "^4.3.5", "vue": "^3.5.13", "vue-gtag-next": "^1.14.0", "vue-i18n": "^10.0.4", "vue-router": "^4.5.0"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.11", "autoprefixer": "^10.4.21", "tailwindcss": "^4.1.11", "tailwindcss-primeui": "^0.6.1", "@playwright/test": "^1.51.1", "@tsconfig/node22": "^22.0.1", "@types/jsdom": "^21.1.7", "@types/node": "^22.14.0", "@vitejs/plugin-vue": "^5.2.3", "@vitejs/plugin-vue-jsx": "^4.1.2", "@vitest/eslint-plugin": "^1.1.39", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.0", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "eslint": "^9.22.0", "eslint-plugin-playwright": "^2.2.0", "eslint-plugin-vue": "~10.0.0", "jiti": "^2.4.2", "jsdom": "^26.0.0", "npm-run-all2": "^7.0.2", "prettier": "3.5.3", "typescript": "~5.8.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2", "vitest": "^3.1.1", "vue-tsc": "^2.2.8"}}