<template>
  <Select
    v-model="selectedLanguage"
    @change="handleLanguageChange"
    :options="languages"
    optionLabel="nativeName"
    optionValue="code"
    class="language-selector"
    :placeholder="t('common.language')"
  >
    <template #value="slotProps">
      <div v-if="slotProps.value" class="language-option">
        <span class="language-flag">{{ getFlag(slotProps.value) }}</span>
        <span class="language-name">{{ getLanguageName(slotProps.value) }}</span>
      </div>
      <span v-else>{{ t('common.language') }}</span>
    </template>
    <template #option="slotProps">
      <div class="language-option">
        <span class="language-flag">{{ getFlag(slotProps.option.code) }}</span>
        <span class="language-name">{{ slotProps.option.nativeName }}</span>
      </div>
    </template>
  </Select>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import Select from 'primevue/select'
import { changeLanguage, getCurrentLanguage, getAvailableLanguages } from '@/i18n'

const { t, locale } = useI18n()
const selectedLanguage = ref('')
const languages = ref(getAvailableLanguages())

const languageFlags = {
  en: '🇺🇸',
  hi: '🇮🇳',
  kn: '🇮🇳'
}

const getFlag = (code) => {
  return languageFlags[code] || '🌐'
}

const getLanguageName = (code) => {
  const lang = languages.value.find(l => l.code === code)
  return lang ? lang.nativeName : code
}

const handleLanguageChange = (event) => {
  const newLanguage = event.value
  changeLanguage(newLanguage)
  selectedLanguage.value = newLanguage
}

// Watch for locale changes to keep the selector in sync
watch(locale, (newLocale) => {
  selectedLanguage.value = newLocale
})

onMounted(() => {
  selectedLanguage.value = getCurrentLanguage()
})
</script>

<style scoped>
.language-selector {
  min-width: 120px;
}

.language-option {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.language-flag {
  font-size: 1.2rem;
}

.language-name {
  font-size: 0.9rem;
  font-weight: 500;
}

:deep(.p-dropdown) {
  border-radius: 8px !important;
}

:deep(.p-dropdown-label) {
  padding: 0.5rem 0.75rem !important;
}
</style>