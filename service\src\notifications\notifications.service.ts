import { Injectable, NotFoundException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import {
  Notification,
  NotificationType,
  NotificationPriority,
} from './entities/notification.entity';
import { NotificationPreference } from './entities/notification-preference.entity';
import { UsersService } from '../users/services/users.service';
import { JobService } from '../jobs/services/job.service';
import { NotificationsGateway } from './notifications.gateway';
import { NotificationTemplatesService } from './notification-templates.service';
import { UserEntity } from '@/users/entities/user.entity';

@Injectable()
export class NotificationsService {
  private readonly logger = new Logger(NotificationsService.name);

  constructor(
    @InjectRepository(Notification)
    private notificationsRepository: Repository<Notification>,
    @InjectRepository(NotificationPreference)
    private preferencesRepository: Repository<NotificationPreference>,
    private usersService: UsersService,
    private jobsService: JobService,
    private notificationsGateway: NotificationsGateway,
    private templatesService: NotificationTemplatesService,
  ) {}

  async create(createNotificationDto: {
    userId: string;
    type: NotificationType;
    data: Record<string, any>;
  }): Promise<Notification> {
    const { userId, type, data } = createNotificationDto;
    this.logger.log(`Creating notification for user ${userId}, type: ${type}`);

    // Get user preferences
    const preferences = await this.getUserPreferences(userId);
    if (!preferences) {
      this.logger.warn(`User preferences not found for user ${userId}`);
      throw new NotFoundException('User preferences not found');
    }

    // Check if user wants to receive this type of notification
    if (!preferences.inAppPreferences[type]) {
      this.logger.debug(`User ${userId} has disabled notifications for type ${type}`);
      return null;
    }

    // Get template for notification type
    const template = this.templatesService.getTemplate(type, data);

    const notification = this.notificationsRepository.create({
      type,
      title: template.title,
      message: template.message,
      data: JSON.stringify(data),
      isRead: false,
      recipientId: userId,
      priority: NotificationPriority.MEDIUM,
    });

    const savedNotification = await this.notificationsRepository.save(notification);
    this.logger.log(`Notification created with ID: ${savedNotification.id}`);

    // Send real-time notification if enabled
    if (preferences.inAppPreferences[type]) {
      try {
        await this.notificationsGateway.sendToUser(userId, savedNotification);
        this.logger.debug(`Real-time notification sent to user ${userId}`);
      } catch (error) {
        this.logger.error('Failed to send real-time notification', error);
      }
    }

    return savedNotification;
  }

  async findAll(
    userId: string,
    options: {
      isRead?: boolean;
      type?: NotificationType;
      page?: number;
      limit?: number;
    } = {},
  ): Promise<{ items: Notification[]; total: number }> {
    const { isRead, type, page = 1, limit = 10 } = options;
    const skip = (page - 1) * limit;

    this.logger.debug(`Fetching notifications for user ${userId}, page: ${page}, limit: ${limit}`);

    const queryBuilder = this.notificationsRepository
      .createQueryBuilder('notification')
      // .select(['notification.id', 'notification.data'])
      .where('notification.recipientId = :userId', { userId });

    if (isRead !== undefined) {
      queryBuilder.andWhere('notification.isRead = :isRead', { isRead });
    }

    if (type) {
      queryBuilder.andWhere('notification.type = :type', { type });
    }

    const [items, total] = await queryBuilder
      .orderBy('notification.createdAt', 'DESC')
      .skip(skip)
      .take(limit)
      .getManyAndCount();

    this.logger.debug(`Found ${items.length} notifications out of ${total} total for user ${userId}`);
    return { items, total };
  }

  async markAsRead(id: string, userId: string): Promise<Notification> {
    this.logger.log(`Marking notification ${id} as read for user ${userId}`);
    
    const notification = await this.notificationsRepository.findOne({
      where: { id, recipientId: userId },
    });

    if (!notification) {
      this.logger.warn(`Notification ${id} not found for user ${userId}`);
      throw new NotFoundException('Notification not found');
    }

    notification.isRead = true;
    const updatedNotification = await this.notificationsRepository.save(notification);

    // Notify through WebSocket
    try {
      this.notificationsGateway.sendToUser(userId, updatedNotification);
      this.logger.debug(`WebSocket notification sent for marked as read`);
    } catch (error) {
      this.logger.error('Failed to send WebSocket notification for mark as read', error);
    }

    return updatedNotification;
  }

  async markAllAsRead(userId: string): Promise<void> {
    this.logger.log(`Marking all notifications as read for user ${userId}`);
    
    const result = await this.notificationsRepository.update(
      { recipientId: userId, isRead: false },
      { isRead: true },
    );

    this.logger.debug(`Updated ${result.affected} notifications for user ${userId}`);

    // Notify through WebSocket
    try {
      this.notificationsGateway.sendToUser(userId, {
        id: 'all',
        type: NotificationType.SYSTEM,
        title: 'All notifications marked as read',
        message: 'All your notifications have been marked as read',
        data: JSON.stringify({ userId }),
        isRead: true,
        recipientId: userId,
        priority: NotificationPriority.LOW,
        createdAt: new Date(),
        updatedAt: new Date(),
        recipient: null,
      } as unknown as Notification);
      this.logger.debug(`WebSocket notification sent for mark all as read`);
    } catch (error) {
      this.logger.error('Failed to send WebSocket notification for mark all as read', error);
    }
  }

  async delete(id: string, userId: string): Promise<void> {
    this.logger.log(`Deleting notification ${id} for user ${userId}`);
    
    const result = await this.notificationsRepository.delete({ id, recipientId: userId });
    if (result.affected === 0) {
      this.logger.warn(`Notification ${id} not found for user ${userId}`);
      throw new NotFoundException('Notification not found');
    }
    
    this.logger.debug(`Successfully deleted notification ${id} for user ${userId}`);
  }

  async getUserPreferences(userId: string): Promise<NotificationPreference> {
    let preferences = await this.preferencesRepository.findOne({
      where: { userId },
    });

    if (!preferences) {
      // Create default preferences
      preferences = this.preferencesRepository.create({
        userId,
        emailPreferences: {
          [NotificationType.JOB_CREATED]: true,
          [NotificationType.JOB_UPDATED]: true,
          [NotificationType.JOB_APPLIED]: true,
          [NotificationType.APPLICATION_STATUS]: true,
          [NotificationType.SYSTEM]: true,
        },
        pushPreferences: {
          [NotificationType.JOB_CREATED]: true,
          [NotificationType.JOB_UPDATED]: true,
          [NotificationType.JOB_APPLIED]: true,
          [NotificationType.APPLICATION_STATUS]: true,
          [NotificationType.SYSTEM]: true,
        },
        inAppPreferences: {
          [NotificationType.JOB_CREATED]: true,
          [NotificationType.JOB_UPDATED]: true,
          [NotificationType.JOB_APPLIED]: true,
          [NotificationType.APPLICATION_STATUS]: true,
          [NotificationType.SYSTEM]: true,
        },
        receiveJobAlerts: true,
        receiveApplicationUpdates: true,
        receiveSystemNotifications: true,
        emailDigest: false,
        emailDigestFrequency: 'DAILY',
      });
      await this.preferencesRepository.save(preferences);
    }

    return preferences;
  }

  async updatePreferences(
    userId: string,
    updatePreferencesDto: Partial<NotificationPreference>,
  ): Promise<NotificationPreference> {
    const preferences = await this.getUserPreferences(userId);
    Object.assign(preferences, updatePreferencesDto);
    return this.preferencesRepository.save(preferences);
  }

  async notifyJobCreated(
    params: { jobId: string; employerId: string },
    user: UserEntity,
  ): Promise<void> {
    this.logger.log(`Notifying job created for job ${params.jobId} by employer ${params.employerId}`);
    
    const job = await this.jobsService.findOne(params.jobId, user);
    if (!job) {
      this.logger.warn(`Job ${params.jobId} not found for notification`);
      throw new NotFoundException('Job not found');
    }

    // Get all users who might be interested in this job
    const { users } = await this.usersService.getUsers();
    this.logger.debug(`Found ${users.length} users to notify about job creation`);

    // Create notifications for each user
    const notifications = users.map((user) => ({
      userId: user.id,
      type: NotificationType.JOB_CREATED,
      data: {
        jobId: job.id,
        jobTitle: job.title,
        employerName: job.employer.firstName + ' ' + job.employer.lastName,
      },
    }));

    // Create notifications in parallel
    const results = await Promise.allSettled(notifications.map((notification) => this.create(notification)));
    const successful = results.filter(result => result.status === 'fulfilled').length;
    const failed = results.filter(result => result.status === 'rejected').length;
    
    this.logger.log(`Job creation notifications: ${successful} successful, ${failed} failed`);
  }

  async notifyJobUpdated(
    params: { jobId: string; employerId: string },
    user: UserEntity,
  ): Promise<void> {
    const job = await this.jobsService.findOne(params.jobId, user);
    if (!job) {
      throw new NotFoundException('Job not found');
    }

    // Get all users who have applied to this job
    const applications = job.applications || [];

    // Create notifications for each applicant
    const notifications = applications.map((application) => ({
      userId: application.applicant.id,
      type: NotificationType.JOB_UPDATED,
      data: {
        jobId: job.id,
        jobTitle: job.title,
        employerName: job.employer.firstName + ' ' + job.employer.lastName,
      },
    }));

    // Create notifications in parallel
    await Promise.all(notifications.map((notification) => this.create(notification)));
  }

  async notifyApplicationStatus(
    params: { applicationId: string; status: string },
    user: UserEntity,
  ): Promise<void> {
    const job = await this.jobsService.findOne(params.applicationId, user);
    if (!job) {
      throw new NotFoundException('Job not found');
    }

    const application = job.applications?.find((app) => app.id === params.applicationId);
    if (!application) {
      throw new NotFoundException('Application not found');
    }

    await this.create({
      userId: application.applicant.id,
      type: NotificationType.APPLICATION_STATUS,
      data: {
        applicationId: application.id,
        jobId: job.id,
        jobTitle: job.title,
        status: params.status,
      },
    });
  }

  async notifyJobApplied(
    params: { jobId: string; applicantId: string },
    user: UserEntity,
  ): Promise<void> {
    // Find the job and its employer
    const job = await this.jobsService.findOne(params.jobId, user);
    if (!job) {
      throw new NotFoundException('Job not found');
    }
    const employer = job.employer;
    if (!employer) {
      throw new NotFoundException('Employer not found for this job');
    }

    // Find the applicant
    const applicant = await this.usersService.findOne(params.applicantId);
    if (!applicant) {
      throw new NotFoundException('Applicant not found');
    }

    // Create notification for the employer
    await this.create({
      userId: employer.id,
      type: NotificationType.JOB_APPLIED,
      data: {
        jobId: job.id,
        jobTitle: job.title,
        applicantId: applicant.id,
        applicantName: applicant.firstName + ' ' + applicant.lastName,
      },
    });
  }
}
