import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { applicationsService } from '@/api/services/applicationsService'
import { formatApiError } from '@/utils/apiHelpers'

export const useApplicationsStore = defineStore('applications', () => {
  // State
  const applications = ref([])
  const currentApplication = ref(null)
  const isLoading = ref(false)
  const error = ref(null)
  const pagination = ref({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
    hasNextPage: false,
    hasPreviousPage: false
  })
  
  // Filters state
  const filters = ref({
    status: 'all',
    jobId: null,
    search: '',
    dateRange: null,
    sortBy: 'createdAt',
    sortOrder: 'desc'
  })

  // Getters
  const totalApplications = computed(() => pagination.value.total)
  const hasApplications = computed(() => applications.value.length > 0)
  
  // Filtered applications by status
  const pendingApplications = computed(() => 
    applications.value.filter(app => 
      app.status === 'PENDING' || app.status === 'UNDER_REVIEW'
    )
  )
  
  const interviewApplications = computed(() => 
    applications.value.filter(app => app.status === 'INTERVIEW')
  )
  
  const offerApplications = computed(() => 
    applications.value.filter(app => app.status === 'OFFER')
  )
  
  const rejectedApplications = computed(() => 
    applications.value.filter(app => app.status === 'REJECTED')
  )
  
  const withdrawnApplications = computed(() => 
    applications.value.filter(app => app.status === 'WITHDRAWN')
  )

  // Filtered applications based on active filter
  const filteredApplications = computed(() => {
    switch (filters.value.status) {
      case 'pending':
        return pendingApplications.value
      case 'interview':
        return interviewApplications.value
      case 'offer':
        return offerApplications.value
      case 'rejected':
        return rejectedApplications.value
      case 'withdrawn':
        return withdrawnApplications.value
      default:
        return applications.value
    }
  })

  // Stats
  const stats = computed(() => ({
    total: applications.value.length,
    pending: pendingApplications.value.length,
    interview: interviewApplications.value.length,
    offer: offerApplications.value.length,
    rejected: rejectedApplications.value.length,
    withdrawn: withdrawnApplications.value.length
  }))

  // Actions
  const fetchJobApplicants = async (params = {}) => {
    isLoading.value = true
    error.value = null

    try {
      const queryParams = {
        ...filters.value,
        ...params,
        page: pagination.value.page,
        limit: pagination.value.limit
      }
      
      const response = await applicationsService.getJobApplicants(queryParams)
      
      if (response.success) {
        // If it's a new search (page 1), replace applications; otherwise append for pagination
        if (queryParams.page === 1) {
          applications.value = response.data
        } else {
          applications.value = [...applications.value, ...response.data]
        }
        
        if (response.pagination) {
          pagination.value = {
            page: response.pagination.page,
            limit: response.pagination.limit,
            total: response.pagination.total,
            totalPages: response.pagination.totalPages,
            hasNextPage: response.pagination.hasNextPage,
            hasPreviousPage: response.pagination.hasPreviousPage
          }
        }
      } else {
        throw new Error(response.message || 'Failed to fetch job applicants')
      }
    } catch (err) {
      error.value = formatApiError(err)
      console.error('Fetch job applicants error:', err)
    } finally {
      isLoading.value = false
    }
  }

  const fetchApplicationById = async (id) => {
    isLoading.value = true
    error.value = null

    try {
      const response = await applicationsService.getById(id)
      
      if (response.success) {
        currentApplication.value = response.data
        return response.data
      } else {
        throw new Error(response.message || 'Application not found')
      }
    } catch (err) {
      error.value = formatApiError(err)
      console.error('Fetch application error:', err)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const updateApplicationStatus = async (applicationId, status, comment = '') => {
    isLoading.value = true
    error.value = null

    try {
      const response = await applicationsService.update(applicationId, { status, comment })
      
      if (response.success) {
        // Update the application in the list
        const index = applications.value.findIndex(app => app.id === applicationId)
        if (index !== -1) {
          applications.value[index] = response.data
        }
        
        if (currentApplication.value && currentApplication.value.id === applicationId) {
          currentApplication.value = response.data
        }
        
        return response.data
      } else {
        throw new Error(response.message || 'Failed to update application status')
      }
    } catch (err) {
      error.value = formatApiError(err)
      console.error('Update application status error:', err)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const withdrawApplication = async (applicationId) => {
    isLoading.value = true
    error.value = null

    try {
      const response = await applicationsService.withdraw(applicationId)
      
      if (response.success) {
        // Update the application status in the list
        const index = applications.value.findIndex(app => app.id === applicationId)
        if (index !== -1) {
          applications.value[index].status = 'WITHDRAWN'
        }
        
        if (currentApplication.value && currentApplication.value.id === applicationId) {
          currentApplication.value.status = 'WITHDRAWN'
        }
        
        return response.data
      } else {
        throw new Error(response.message || 'Failed to withdraw application')
      }
    } catch (err) {
      error.value = formatApiError(err)
      console.error('Withdraw application error:', err)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const getApplicationsByJob = async (jobId, params = {}) => {
    isLoading.value = true
    error.value = null

    try {
      const response = await applicationsService.getByJob(jobId, params)
      
      if (response.success) {
        return response.data
      } else {
        throw new Error(response.message || 'Failed to fetch job applications')
      }
    } catch (err) {
      error.value = formatApiError(err)
      console.error('Fetch job applications error:', err)
      return []
    } finally {
      isLoading.value = false
    }
  }

  // Filter and pagination actions
  const setFilters = (newFilters) => {
    filters.value = { ...filters.value, ...newFilters }
    pagination.value.page = 1 // Reset to first page when filters change
  }

  const clearFilters = () => {
    filters.value = {
      status: 'all',
      jobId: null,
      search: '',
      dateRange: null,
      sortBy: 'createdAt',
      sortOrder: 'desc'
    }
    pagination.value.page = 1
  }

  const setPage = (page) => {
    pagination.value.page = page
  }

  const setPageSize = (limit) => {
    pagination.value.limit = limit
    pagination.value.page = 1
  }

  const clearError = () => {
    error.value = null
  }

  const clearCurrentApplication = () => {
    currentApplication.value = null
  }

  const reset = () => {
    applications.value = []
    currentApplication.value = null
    isLoading.value = false
    error.value = null
    pagination.value = {
      page: 1,
      limit: 10,
      total: 0,
      totalPages: 0,
      hasNextPage: false,
      hasPreviousPage: false
    }
    clearFilters()
  }

  return {
    // State
    applications,
    currentApplication,
    isLoading,
    error,
    pagination,
    filters,
    
    // Getters
    totalApplications,
    hasApplications,
    pendingApplications,
    interviewApplications,
    offerApplications,
    rejectedApplications,
    withdrawnApplications,
    filteredApplications,
    stats,
    
    // Actions
    fetchJobApplicants,
    fetchApplicationById,
    updateApplicationStatus,
    withdrawApplication,
    getApplicationsByJob,
    setFilters,
    clearFilters,
    setPage,
    setPageSize,
    clearError,
    clearCurrentApplication,
    reset
  }
}) 