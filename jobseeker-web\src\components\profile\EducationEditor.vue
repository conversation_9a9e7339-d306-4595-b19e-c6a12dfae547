<template>
  <Dialog 
    :visible="localVisible" 
    @update:visible="localVisible = $event"
    :header="isEditing ? 'Edit Education' : 'Add Education'"
    :modal="true"
    :closable="true"
    class="education-dialog"
    :style="{ width: '600px' }"
    @hide="handleCancel"
  >
    <form @submit.prevent="handleSubmit" class="education-form">
      <div class="form-grid">
        <div class="form-group full-width">
          <label for="degree">Degree/Certificate *</label>
          <InputText
            id="degree"
            v-model="formData.degree"
            :class="{ 'p-invalid': errors.degree }"
            placeholder="e.g., Construction Technology Certificate"
          />
          <small v-if="errors.degree" class="p-error">{{ errors.degree }}</small>
        </div>

        <div class="form-group full-width">
          <label for="school">School/Institution *</label>
          <InputText
            id="school"
            v-model="formData.school"
            :class="{ 'p-invalid': errors.school }"
            placeholder="e.g., Mumbai Technical College"
          />
          <small v-if="errors.school" class="p-error">{{ errors.school }}</small>
        </div>

        <div class="form-group">
          <label for="startYear">Start Year *</label>
          <Select
            id="startYear"
            v-model="formData.startYear"
            :options="yearOptions"
            :class="{ 'p-invalid': errors.startYear }"
            placeholder="Select year"
          />
          <small v-if="errors.startYear" class="p-error">{{ errors.startYear }}</small>
        </div>

        <div class="form-group">
          <label for="endYear">End Year</label>
          <div class="end-year-wrapper">
            <Select
              id="endYear"
              v-model="formData.endYear"
              :options="yearOptions"
              :class="{ 'p-invalid': errors.endYear }"
              placeholder="Select year"
              :disabled="formData.currentEducation"
            />
            <div class="current-education-checkbox">
              <Checkbox
                id="currentEducation"
                v-model="formData.currentEducation"
                :binary="true"
              />
              <label for="currentEducation">Currently Studying</label>
            </div>
          </div>
          <small v-if="errors.endYear" class="p-error">{{ errors.endYear }}</small>
        </div>

        <div class="form-group full-width">
          <label for="fieldOfStudy">Field of Study</label>
          <InputText
            id="fieldOfStudy"
            v-model="formData.fieldOfStudy"
            placeholder="e.g., Construction Technology"
          />
        </div>
      </div>
    </form>

    <template #footer>
      <div class="dialog-footer">
        <Button 
          @click="handleCancel"
          label="Cancel"
          outlined
          :disabled="isSaving"
        />
        <Button 
          @click="handleSubmit"
          :label="isEditing ? 'Update Education' : 'Add Education'"
          :loading="isSaving"
          class="save-btn"
        />
      </div>
    </template>
  </Dialog>
</template>

<script setup>
import { ref, watch, computed } from 'vue'
import Dialog from 'primevue/dialog'
import InputText from 'primevue/inputtext'
import Select from 'primevue/select'
import Checkbox from 'primevue/checkbox'
import Button from 'primevue/button'
import alertManager from '@/utils/alertManager'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  educationData: {
    type: Object,
    default: () => ({})
  },
  isEditing: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:visible', 'save'])

const isSaving = ref(false)
const errors = ref({})

const formData = ref({
  id: null,
  degree: '',
  school: '',
  startYear: null,
  endYear: null,
  currentEducation: false,
  fieldOfStudy: ''
})

// Computed property to handle v-model for visible prop
const localVisible = computed({
  get() {
    return props.visible
  },
  set(value) {
    emit('update:visible', value)
  }
})

// Generate year options from 1970 to current year
const yearOptions = computed(() => {
  const currentYear = new Date().getFullYear()
  const years = []
  for (let year = currentYear; year >= 1970; year--) {
    years.push(year.toString())
  }
  return years
})

const resetForm = () => {
  formData.value = {
    id: null,
    degree: '',
    school: '',
    startYear: null,
    endYear: null,
    currentEducation: false,
    fieldOfStudy: ''
  }
  errors.value = {}
}

// Watch for prop changes
watch(() => props.educationData, (newData) => {
  if (newData && props.isEditing) {
    formData.value = {
      id: newData.id,
      degree: newData.degree || '',
      school: newData.school || '',
      startYear: newData.startYear || null,
      endYear: newData.endYear || null,
      currentEducation: !newData.endYear,
      fieldOfStudy: newData.fieldOfStudy || ''
    }
  } else {
    resetForm()
  }
}, { immediate: true, deep: true })

const validateForm = () => {
  errors.value = {}
  
  if (!formData.value.degree?.trim()) {
    errors.value.degree = 'Degree/Certificate is required'
  }
  
  if (!formData.value.school?.trim()) {
    errors.value.school = 'School/Institution is required'
  }
  
  if (!formData.value.startYear) {
    errors.value.startYear = 'Start year is required'
  }
  
  if (!formData.value.currentEducation && !formData.value.endYear) {
    errors.value.endYear = 'Please provide an end year or mark as currently studying'
  }
  
  if (formData.value.startYear && formData.value.endYear && 
      parseInt(formData.value.startYear) > parseInt(formData.value.endYear)) {
    errors.value.endYear = 'End year cannot be before start year'
  }
  
  return Object.keys(errors.value).length === 0
}

const handleSubmit = async () => {
  if (!validateForm()) {
    return
  }
  
  isSaving.value = true
  
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const updatedData = {
      ...formData.value,
      endYear: formData.value.currentEducation ? null : formData.value.endYear
    }
    
    emit('save', updatedData)
    emit('update:visible', false)
    
    const message = props.isEditing ? 
      'Education updated successfully!' : 
      'Education added successfully!'
    
    alertManager.showSuccess('Success', message)
  } catch (error) {
    alertManager.showError('Error', 'Failed to save education. Please try again.')
  } finally {
    isSaving.value = false
  }
}

const handleCancel = () => {
  emit('update:visible', false)
  resetForm()
}
</script>

<style scoped>
.education-dialog {
  border-radius: 12px !important;
}

.education-form {
  padding: 1rem 0;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  font-weight: 600;
  color: var(--text-color);
  font-size: 0.9rem;
}

.form-group input,
.form-group .p-dropdown {
  width: 100% !important;
}

.end-year-wrapper {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.current-education-checkbox {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.current-education-checkbox label {
  font-weight: normal;
  font-size: 0.85rem;
}

.dialog-footer {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.save-btn {
  background: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
  }
  
  .dialog-footer {
    flex-direction: column;
  }
}
</style>