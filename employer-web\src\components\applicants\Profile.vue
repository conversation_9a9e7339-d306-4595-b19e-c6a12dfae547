

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import Button from 'primevue/button'
import Avatar from 'primevue/avatar'
import Tag from 'primevue/tag'
import alertManager from '@/utils/alertManager'
import {api}from '@/api'

// const authStore = useAuthStore()
const applicantProfile = ref()
const route = useRoute()
// Reactive profile data
const profile = ref({
  firstName: '',
  lastName: '',
  email: '',
  phone: '',
  dateOfBirth: '',
  location: '',
  jobTitle: '',
  experienceYears: 0,
  experienceMonths: 0,
  industry: '',
  desiredSalary: 0,
  bio: '',
  skills: [],
  profileViews: 0,
  isPublic: true,
  emailNotifications: true,
  avatarColor: '#3b82f6',
  avatarUrl: null,
  workExperience: [],
  education: [],
  address: '',
  city: '',
  state: '',
  country: '',
  postalCode: '',
})

// Watch for auth store changes
watch(() => applicantProfile, (newUser) => {
  if (newUser) {
    profile.value = {
      ...profile.value,
      firstName: newUser.profile?.firstName || '',
      lastName: newUser.profile?.lastName || '',
      email: newUser.email || '',
      phone: newUser.profile?.phoneNumber || newUser.profile?.phone || '',
      dateOfBirth: newUser.profile?.dateOfBirth || '',
      location: newUser.profile?.city && newUser.profile?.state
        ? `${newUser.profile.city}, ${newUser.profile.state}`
        : '',
      avatarUrl: newUser.profile?.profileImage || null,
      // Professional Information
      jobTitle: newUser.profile?.jobTitle || '',
      experienceYears: newUser.profile?.experienceYears || 0,
      experienceMonths: newUser.profile?.experienceMonths || 0,
      industry: newUser.profile?.industry || '',
      desiredSalary: newUser.profile?.desiredSalary || 0,
      bio: newUser.profile?.bio || '',
      skills: newUser.profile?.skills || [],
      workExperience: newUser.profile?.workExperience || [],
      education: newUser.profile?.education || [],
      // Address fields
      address: newUser.profile?.addressLandmark || newUser.profile?.address || '',
      city: newUser.profile?.addressCity || newUser.profile?.city || '',
      state: newUser.profile?.state || '',
      country: newUser.profile?.country || '',
      postalCode: newUser.profile?.pinCode || newUser.profile?.postalCode || '',
    }
  }
}, { immediate: true, deep: true })

// Avatar style computed property
const avatarStyle = computed(() => {
  if (profile.value.avatarUrl) {
    return {
      backgroundImage: `url(${profile.value.avatarUrl})`,
      backgroundSize: 'cover',
      backgroundPosition: 'center',
      color: 'transparent'
    }
  }

  return {
    backgroundColor: profile.value.avatarColor || '#3b82f6'
  }
})

// Mock applications count
const applications = ref([1, 2, 3, 4, 5]) // Mock data

const getInitials = () => {
  const first = profile.value.firstName?.charAt(0) || ''
  const last = profile.value.lastName?.charAt(0) || ''
  return (first + last).toUpperCase() || 'U'
}

const formatDate = (dateString) => {
  if (!dateString) return null
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const shareProfile = async () => {
  if (!profile.value.isPublic) {
    alertManager.showInfo('Profile Not Public', 'Enable profile visibility in privacy settings to share your profile.')
    return
  }

  const profileUrl = `${window.location.origin}/profile/${profile.value.firstName.toLowerCase()}-${profile.value.lastName.toLowerCase()}`

  if (navigator.share) {
    try {
      await navigator.share({
        title: `${profile.value.firstName} ${profile.value.lastName} - Profile`,
        text: `Check out my professional profile on Job Dalal`,
        url: profileUrl
      })
    } catch (error) {
      console.error('Error sharing:', error)
    }
  } else {
    // Fallback to clipboard
    navigator.clipboard.writeText(profileUrl).then(() => {
      alertManager.showSuccess('Link Copied', 'Profile link copied to clipboard')
    }).catch(() => {
      alertManager.showError('Copy Failed', 'Failed to copy profile link')
    })
  }
}

const loadApplicantProfile = async () => {route.query
  const response = await api.profile.getApplicantProfile(route.params.id);

  if (response.success) {
    applicantProfile.value = response.data
  }
}

onMounted(() => {
  loadApplicantProfile();
})
</script>

<template>
    <div class="profile-page flex-1 overflow-x-hidden overflow-y-auto">
      <div class="profile-content">

        <div class="profile-main">
          <!-- Profile Header Card -->
          <div class="profile-header-card">
            <div class="profile-avatar-section">
              <div class="avatar-container">
                <Avatar :label="getInitials()" size="xlarge" class="profile-avatar" :style="avatarStyle" />
              </div>
              <div class="profile-basic-info">
                <h2>{{ profile.firstName }} {{ profile.lastName }}</h2>
                <p class="profile-title">{{ profile.jobTitle || 'Job Seeker' }}</p>
                <p class="profile-location">
                  <i class="pi pi-map-marker"></i>
                  {{ profile.location || 'Location not specified' }}
                </p>
                <div class="profile-stats">
                  <div class="stat">
                    <span class="stat-number">{{ profile.experienceYears || 0 }}</span>
                    <span class="stat-label">Years Experience</span>
                  </div>
                  <div class="stat">
                    <span class="stat-number">{{ applications.length }}</span>
                    <span class="stat-label">Applications</span>
                  </div>
                  <div class="stat">
                    <span class="stat-number">{{ profile.profileViews || 0 }}</span>
                    <span class="stat-label">Profile Views</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Personal Information -->
          <div class="profile-section">
            <div class="section-header">
              <h3>Personal Information</h3>
            </div>
            <div class="info-grid">
              <div class="info-item">
                <label>First Name</label>
                <span>{{ profile.firstName || 'Not specified' }}</span>
              </div>
              <div class="info-item">
                <label>Last Name</label>
                <span>{{ profile.lastName || 'Not specified' }}</span>
              </div>
              <div class="info-item">
                <label>Email</label>
                <span>{{ profile.email }}</span>
              </div>
              <div class="info-item">
                <label>Phone</label>
                <span>{{ profile.phone || 'Not specified' }}</span>
              </div>
              <div class="info-item">
                <label>Date of Birth</label>
                <span>{{ formatDate(profile.dateOfBirth) || 'Not specified' }}</span>
              </div>
              <div class="info-item">
                <label>Location</label>
                <span>{{ profile.location || 'Not specified' }}</span>
              </div>
            </div>
            <div class="info-grid" style="margin-top: 1.5rem;">
              <div class="info-item">
                <label>Address</label>
                <span>{{ profile.address || 'Not specified' }}</span>
              </div>
              <div class="info-item">
                <label>City</label>
                <span>{{ profile.city || 'Not specified' }}</span>
              </div>
              <div class="info-item">
                <label>State</label>
                <span>{{ profile.state || 'Not specified' }}</span>
              </div>
              <div class="info-item">
                <label>Country</label>
                <span>{{ profile.country || 'Not specified' }}</span>
              </div>
              <div class="info-item">
                <label>Postal Code</label>
                <span>{{ profile.postalCode || 'Not specified' }}</span>
              </div>
            </div>
          </div>

          <!-- Professional Information -->
          <div class="profile-section">
            <div class="section-header">
              <h3>Professional Information</h3>
            </div>
            <div class="info-grid">
              <div class="info-item">
                <label>Current Job Title</label>
                <span>{{ profile.jobTitle || 'Not specified' }}</span>
              </div>
              <div class="info-item">
                <label>Years of Experience</label>
                <span>
                  <template v-if="profile.experienceYears && profile.experienceMonths">
                    {{ profile.experienceYears }} year<span v-if="profile.experienceYears > 1">s</span> {{ profile.experienceMonths }} month<span v-if="profile.experienceMonths > 1">s</span>
                  </template>
                  <template v-else-if="profile.experienceYears">
                    {{ profile.experienceYears }} year<span v-if="profile.experienceYears > 1">s</span>
                  </template>
                  <template v-else-if="profile.experienceMonths">
                    {{ profile.experienceMonths }} month<span v-if="profile.experienceMonths > 1">s</span>
                  </template>
                  <template v-else>
                    0
                  </template>
                </span>
              </div>
              <div class="info-item">
                <label>Industry</label>
                <span>{{ profile.industry || 'Not specified' }}</span>
              </div>
              <div class="info-item">
                <label>Desired Salary</label>
                <span>₹{{ profile.desiredSalary?.toLocaleString() || 'Not specified' }}</span>
              </div>
              <div class="info-item full-width">
                <label>Bio</label>
                <span>{{ profile.bio || 'No bio added yet' }}</span>
              </div>
            </div>
          </div>

          <!-- Skills -->
          <div class="profile-section">
            <div class="section-header">
              <h3>Skills</h3>
            </div>
            <div class="skills-container">
              <Tag v-for="skill in profile.skills" :key="skill" :value="skill" class="skill-tag" />
            </div>
          </div>

          <!-- Work Experience -->
          <div class="profile-section">
            <div class="section-header">
              <h3>Work Experience</h3>
            </div>
            <div class="experience-list">
              <div v-for="exp in profile.workExperience" :key="exp.id" class="experience-item">
                <div class="experience-header">
                  <div class="experience-info">
                    <h4>{{ exp.jobTitle }}</h4>
                    <p class="company">{{ exp.company }}</p>
                    <p class="duration">{{ exp.startDate }} - {{ exp.endDate || 'Present' }}</p>
                  </div>
                </div>
                <p class="experience-description">{{ exp.description }}</p>
              </div>
            </div>
          </div>

          <!-- Education -->
          <div class="profile-section">
            <div class="section-header">
              <h3>Education</h3>
            </div>
            <div class="education-list">
              <div v-for="edu in profile.education" :key="edu.id" class="education-item">
                <div class="education-header">
                  <div class="education-info">
                    <h4>{{ edu.degree }}</h4>
                    <p class="school">{{ edu.school }}</p>
                    <p class="duration">{{ edu.startYear }} - {{ edu.endYear || 'Present' }}</p>
                    <p v-if="edu.fieldOfStudy" class="field-of-study">{{ edu.fieldOfStudy }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Sidebar -->
        <div class="profile-sidebar">

          <!-- Quick Actions -->
          <div class="sidebar-card actions-card">
            <h3>Quick Actions</h3>
            <div class="actions-list">
         
              <Button @click="shareProfile" icon="pi pi-share-alt" label="Share Profile" outlined
                class="action-button" />
            </div>
          </div>
        </div>
      </div>
    </div>

</template>

<style scoped>
.profile-page {
  background: linear-gradient(135deg, var(--jobs-bg-start) 0%, var(--jobs-bg-end) 100%);
}

.profile-content {
  margin: 0 auto;
  padding: 1rem;
  display: grid;
  grid-template-columns: 1fr 350px;
  gap: 2rem;
}

.profile-main {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.profile-content .page-header {
  grid-column: 1 / -1;
  margin: 2rem 0 -2rem;
}

.profile-header-card {
  background: var(--surface-card);
  border: 1px solid var(--surface-border);
  border-radius: 16px;
  padding: 2rem;
}

.completion-card,
.profile-header-card {
  margin-top: 2rem;
}

.profile-avatar-section {
  display: flex;
  gap: 2rem;
  align-items: flex-start;
}

.avatar-container {
  position: relative;
}

.profile-avatar {
  width: 120px !important;
  height: 120px !important;
  font-size: 3rem !important;
}

.profile-basic-info {
  flex: 1;
}

.profile-basic-info h2 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-color);
  margin: 0 0 0.5rem 0;
}

.profile-title {
  font-size: 1.25rem;
  color: var(--primary-color);
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.profile-location {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-color-secondary);
  margin: 0 0 1.5rem 0;
}

.profile-location i {
  color: var(--primary-color);
}

.profile-stats {
  display: flex;
  gap: 2rem;
}

.stat {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
}

.stat-label {
  font-size: 0.8rem;
  color: var(--text-color-secondary);
}

.profile-section {
  background: var(--surface-card);
  border: 1px solid var(--surface-border);
  border-radius: 12px;
  padding: 1.5rem;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--surface-border);
}

.section-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

.edit-button {
  color: var(--primary-color) !important;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.info-item.full-width {
  grid-column: 1 / -1;
}

.info-item label {
  font-weight: 600;
  color: var(--text-color-secondary);
  font-size: 0.9rem;
}

.info-item span {
  color: var(--text-color);
  font-size: 1rem;
}

.skills-container {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.skill-tag {
  font-size: 0.85rem !important;
  padding: 0.5rem 1rem !important;
}

.experience-list,
.education-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.experience-item,
.education-item {
  padding: 1rem;
  border: 1px solid var(--surface-border);
  border-radius: 8px;
  background: var(--surface-50);
}

.experience-header,
.education-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.75rem;
}

.experience-info h4,
.education-info h4 {
  margin: 0 0 0.25rem 0;
  color: var(--text-color);
  font-weight: 600;
}

.company,
.school {
  margin: 0 0 0.25rem 0;
  color: var(--primary-color);
  font-weight: 500;
}

.duration,
.field-of-study {
  margin: 0;
  color: var(--text-color-secondary);
  font-size: 0.9rem;
}

.field-of-study {
  margin-top: 0.25rem;
  font-style: italic;
}

.experience-description {
  margin: 0;
  color: var(--text-color-secondary);
  line-height: 1.5;
}

.add-experience-btn,
.add-education-btn {
  align-self: flex-start;
}

.profile-sidebar {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.sidebar-card {
  background: var(--surface-card);
  border: 1px solid var(--surface-border);
  border-radius: 12px;
  padding: 1.5rem;
}

.sidebar-card h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0 0 1rem 0;
}

.completion-progress {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.progress-bar {
  flex: 1;
  height: 8px;
  background: var(--surface-200);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: var(--primary-color);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.completion-percentage {
  font-weight: 600;
  color: var(--primary-color);
}

.completion-tips {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.tip-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: var(--text-color-secondary);
}

.tip-item i {
  color: var(--primary-color);
}

.actions-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.action-button {
  width: 100%;
  justify-content: flex-start !important;
}

.privacy-options {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.privacy-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 1rem;
}

.privacy-info {
  flex: 1;
}

.privacy-info label {
  font-weight: 600;
  color: var(--text-color);
  font-size: 0.9rem;
  display: block;
  margin-bottom: 0.25rem;
}

.privacy-info p {
  margin: 0;
  color: var(--text-color-secondary);
  font-size: 0.8rem;
}

@media (max-width: 768px) {
  .profile-content {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .profile-avatar-section {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .profile-stats {
    justify-content: center;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .experience-header,
  .education-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .privacy-item {
    flex-direction: column;
    align-items: flex-start;
  }
}
</style>