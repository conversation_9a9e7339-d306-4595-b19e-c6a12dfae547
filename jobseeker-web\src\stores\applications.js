import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { applicationsService } from '@/api/services/applicationsService'

export const useApplicationsStore = defineStore('applications', () => {
  const applications = ref([])
  const isLoading = ref(false)
  const error = ref(null)

  // Fetch all applications from the backend
  const fetchApplications = async () => {
    isLoading.value = true
    error.value = null
    try {
      const response = await applicationsService.getUserApplications()
      console.log('Raw applications response:', response) // Debug log
      
      if (response.success) {
        // Handle both array and paginated responses
        const rawApps = Array.isArray(response.data) 
          ? response.data 
          : (response.data?.items || response.data || [])
        
        console.log('Raw applications:', rawApps) // Debug log
        
        // Flatten and normalize job details for UI
        applications.value = rawApps.map(app => ({
          id: app.id,
          jobId: app.jobId || app.job?.id,
          jobTitle: app.jobTitle || app.job?.title || 'Unknown Job',
          company: app.company || app.job?.company || app.job?.employer?.companyName || app.job?.industry?.name || 'Unknown Company',
          location: app.location || app.job?.location || 'No location specified',
          status: app.status?.toUpperCase() || 'PENDING',
          appliedDate: app.createdAt || app.appliedDate || new Date().toISOString(),
          color: app.color || app.job?.color || '#3b82f6',
          // Add any additional fields needed for display
          job: app.job || null
        })).filter(app => app.jobId) // Only keep applications with valid jobId
        
        console.log('Processed applications:', applications.value) // Debug log
      } else {
        error.value = response.message || 'Failed to fetch applications'
        console.error('Failed to fetch applications:', response.message)
      }
    } catch (err) {
      error.value = err.message || 'Failed to fetch applications'
      console.error('Error fetching applications:', err)
    } finally {
      isLoading.value = false
    }
  }

  // Apply to a job (create application in backend)
  const addApplication = async (job) => {
    isLoading.value = true
    error.value = null
    try {
      // Create the application through the API
      const response = await applicationsService.create({
        jobId: job.id,
        // Add any additional application data here if needed
        coverLetter: '', // Optional: Add if your form collects this
      })

      // If we got a success response (even without data), create an application object
      if (response.success) {
        // Fetch the latest applications to ensure we have the most up-to-date data
        await fetchApplications()
        
        // Return the newly created application
        return applications.value.find(app => app.jobId === job.id) || {
          id: Date.now().toString(), // Temporary ID until we refresh
          jobId: job.id,
          jobTitle: job.title,
          company: job.company,
          location: job.location,
          color: job.color,
          status: 'PENDING',
          appliedDate: new Date().toISOString(),
          ...(response.data || {})
        }
      } else {
        throw new Error(response.message || 'Failed to apply')
      }
    } catch (err) {
      error.value = err.message || 'Failed to apply'
      throw error.value
    } finally {
      isLoading.value = false
    }
  }

  const handleFiltersUpdate = (newFilters) => {
    console.log('Filters changed:', newFilters)
    filters.value = newFilters
    // jobsStore.fetchJobs() if using API
  }

  const filteredJobs = computed(() => {
    console.log('Filtering jobs with:', filters.value)
    // ...rest of filter logic
  })

  return {
    applications,
    isLoading,
    error,
    fetchApplications,
    addApplication,
    handleFiltersUpdate,
    filteredJobs,
  }
}) 