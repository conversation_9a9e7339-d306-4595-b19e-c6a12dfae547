import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { api } from '@/api'
import { formatApiError } from '@/utils/apiHelpers'

export const useDashboardStore = defineStore('dashboard', () => {
  // State
  const stats = ref({
    // Job statistics
    totalJobs: 0,
    activeJobs: 0,
    recentJobs: 0,
    featuredJobs: 0,
    urgentJobs: 0,
    
    // User statistics
    totalApplications: 0,
    pendingApplications: 0,
    interviewInvitations: 0,
    jobOffers: 0,
    profileViews: 0,
    savedJobs: 0,
    
    // Salary insights
    avgSalary: 0,
    salaryRange: { min: 0, max: 0 },
    
    // Growth metrics
    jobGrowthRate: 0,
    applicationSuccessRate: 0,
    
    // Collections
    topDepartments: [],
    recentActivity: [],
    recommendedJobs: [],
    
    // Market insights
    marketInsights: {
      hotSkills: [],
      trendingLocations: [],
      salaryTrends: {}
    },
    
    // Application insights
    applicationInsights: {
      bestTimeToApply: '',
      avgResponseTime: '',
      competitionLevel: '',
      successTips: []
    }
  })
  
  const isLoading = ref(false)
  const error = ref(null)
  const lastUpdated = ref(null)

  // Getters
  const hasStats = computed(() => stats.value.totalJobs > 0)
  
  const jobGrowthRate = computed(() => {
    if (stats.value.totalJobs === 0) return 0
    return Math.round((stats.value.recentJobs / stats.value.totalJobs) * 100)
  })
  
  const applicationRate = computed(() => {
    if (stats.value.totalJobs === 0) return 0
    return Math.round(stats.value.totalApplications / stats.value.totalJobs)
  })
  
  const topDepartment = computed(() => {
    return stats.value.topDepartments.length > 0 ? stats.value.topDepartments[0] : null
  })
  
  const formattedAvgSalary = computed(() => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(stats.value.avgSalary)
  })

  // Actions
  const fetchStats = async () => {
    isLoading.value = true
    error.value = null

    try {
      const response = await api.dashboard.getStats()
      
      if (response.success) {
        stats.value = { ...stats.value, ...{
          recentActivity: response.data?.recentActivity?.jobs ||[]
        } } ;
        stats.value = { ...stats.value, ...response.data.statistics }
        lastUpdated.value = new Date().toISOString()
      } else {
        throw new Error(response.message || 'Failed to fetch dashboard stats')
      }
    } catch (err) {
      error.value = formatApiError(err)
      console.error('Fetch dashboard stats error:', err)
    } finally {
      isLoading.value = false
    }
  }

  const fetchRecentActivity = async (limit = 10) => {
    try {
      const response = await api.dashboard.getRecentActivity(limit)
      
      if (response.success) {
        stats.value.recentActivity = response.data
      }
    } catch (err) {
      console.error('Fetch recent activity error:', err)
    }
  }

  const fetchRecommendations = async (limit = 5) => {
    try {
      const response = await api.dashboard.getRecommendations(limit)
      
      if (response.success) {
        stats.value.recommendedJobs = response.data
        return response.data
      } else {
        return []
      }
    } catch (err) {
      console.error('Fetch recommendations error:', err)
      return []
    }
  }

  const refreshStats = async () => {
    await Promise.all([
      fetchStats(),
      fetchRecentActivity(),
      fetchRecommendations()
    ])
  }

  const clearError = () => {
    error.value = null
  }

  const getStatsAge = () => {
    if (!lastUpdated.value) return null
    
    const now = new Date()
    const updated = new Date(lastUpdated.value)
    const diffMinutes = Math.floor((now - updated) / (1000 * 60))
    
    if (diffMinutes < 1) return 'Just now'
    if (diffMinutes < 60) return `${diffMinutes} minutes ago`
    
    const diffHours = Math.floor(diffMinutes / 60)
    if (diffHours < 24) return `${diffHours} hours ago`
    
    const diffDays = Math.floor(diffHours / 24)
    return `${diffDays} days ago`
  }

  // Auto-refresh stats every 5 minutes when the store is active
  let refreshInterval = null
  
  const startAutoRefresh = () => {
    if (refreshInterval) return
    
    refreshInterval = setInterval(() => {
      fetchStats()
    }, 5 * 60 * 1000) // 5 minutes
  }
  
  const stopAutoRefresh = () => {
    if (refreshInterval) {
      clearInterval(refreshInterval)
      refreshInterval = null
    }
  }

  return {
    // State
    stats,
    isLoading,
    error,
    lastUpdated,
    // Getters
    hasStats,
    jobGrowthRate,
    applicationRate,
    topDepartment,
    formattedAvgSalary,
    // Actions
    fetchStats,
    fetchRecentActivity,
    fetchRecommendations,
    refreshStats,
    clearError,
    getStatsAge,
    startAutoRefresh,
    stopAutoRefresh
  }
})