<script setup>
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router'
import But<PERSON> from 'primevue/button';
import { navOptions } from './NavOptions'

const route = useRoute()
const router = useRouter()
const visibleItem = ref(null)

const onNavBtnClick = (item) => {
    console.log(item)
    visibleItem.value = item.text
    router.push(item.to)
}

onMounted(() => {
    const activeNav = navOptions.find(item => item.to === route.path);

    if (activeNav) {
        visibleItem.value = activeNav.text
    }
})
</script>
<template>
    <div class="flex flex-col side-nav">
        <Button v-for="(item, index) in navOptions" :key="index" v-tooltip.left="item.text" severity="secondary"
            :icon="`pi pi-${item.icon}`" iconPos="top" @click="onNavBtnClick(item)" class="nav-item p-2"
            :class="visibleItem === item.text ? 'active' : ''" icon-class="btn-icon"/>
    </div>
</template>

<style scoped>
.side-nav {
    width: 4rem;
    gap: 1rem;
    padding-top: 1rem;
}
:deep(button) {
    width: 100%;
    background-color: transparent;
    border-color: transparent;
    padding: 1rem;
}
:deep(.btn-icon) {
    font-size: 1.4rem;
}
.active {
    background: var(--p-button-link-color);
    color: #fff;
}
</style>